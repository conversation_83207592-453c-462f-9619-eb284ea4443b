#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
WAV格式插件

MIT License
Copyright (c) 2025 Music Guru
"""

import os
from typing import Tuple, Optional, Dict, Any

from utils.audio_format_plugin import AudioFormatPlugin

try:
    import librosa
    import librosa.display
    import wave
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    DEPENDENCIES_AVAILABLE = False


class WAVPlugin(AudioFormatPlugin):
    """WAV格式插件"""
    
    def __init__(self):
        """初始化WAV格式插件"""
        super().__init__()
        self.name = "WAV格式"
        self.description = "支持WAV音频格式"
        self.extensions = ["wav"]
    
    def can_handle(self, file_path: str) -> bool:
        """
        检查是否可以处理指定文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            bool: 是否可以处理
        """
        if not DEPENDENCIES_AVAILABLE:
            return False
        
        ext = self.get_file_extension(file_path)
        if ext not in self.extensions:
            return False
        
        try:
            # 尝试加载文件头
            with open(file_path, 'rb') as f:
                header = f.read(12)
                # 检查WAV文件头标识
                return header.startswith(b'RIFF') and b'WAVE' in header
        except Exception:
            return False
    
    def load_audio(self, file_path: str) -> Tuple[Optional[Any], Optional[int]]:
        """
        加载音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Tuple[Optional[Any], Optional[int]]: 音频数据和采样率
        """
        if not DEPENDENCIES_AVAILABLE:
            return None, None
        
        try:
            audio_data, sample_rate = librosa.load(file_path, sr=None)
            return audio_data, sample_rate
        except Exception as e:
            print(f"加载WAV文件失败: {e}")
            return None, None
    
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        获取音频元数据
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Dict[str, Any]: 元数据字典
        """
        metadata = {
            "title": os.path.basename(file_path).split('.')[0],  # 使用文件名作为标题
            "artist": "",
            "album": "",
            "year": "",
            "genre": "",
            "duration": 0
        }
        
        if not DEPENDENCIES_AVAILABLE:
            return metadata
        
        try:
            with wave.open(file_path, 'rb') as wav_file:
                # 获取基本信息
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()
                frame_rate = wav_file.getframerate()
                n_frames = wav_file.getnframes()
                
                # 计算时长
                duration = n_frames / frame_rate
                metadata["duration"] = duration
                
                # 添加技术信息
                metadata["channels"] = channels
                metadata["sample_width"] = sample_width
                metadata["frame_rate"] = frame_rate
            
            return metadata
        except Exception as e:
            print(f"获取WAV元数据失败: {e}")
            return metadata
    
    def get_lyrics(self, file_path: str) -> Optional[str]:
        """
        获取歌词
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Optional[str]: 歌词文本
        """
        # WAV格式通常不包含歌词，尝试查找同名LRC文件
        lrc_path = os.path.splitext(file_path)[0] + ".lrc"
        if os.path.exists(lrc_path):
            try:
                with open(lrc_path, 'r', encoding='utf-8') as f:
                    return f.read()
            except Exception as e:
                print(f"读取LRC文件失败: {e}")
        
        return None
