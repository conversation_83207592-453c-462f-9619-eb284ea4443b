#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
TXT歌词格式插件

MIT License
Copyright (c) 2025 Music Guru
"""

import os
from typing import Dict, Optional
from utils.lyrics_format_manager import LyricsFormatPlugin


class TxtLyricsPlugin(LyricsFormatPlugin):
    """TXT歌词格式插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "纯文本歌词"
        self.description = "支持纯文本格式的歌词文件"
        self.file_extensions = [".txt", ".text"]
    
    def can_handle(self, file_path: str) -> bool:
        """检查是否能处理指定文件"""
        ext = os.path.splitext(file_path)[1].lower()
        return ext in self.file_extensions
    
    def read_lyrics(self, file_path: str) -> Optional[str]:
        """读取TXT歌词文件"""
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin1']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read().strip()
                        if content:
                            self.logger.info(f"成功读取TXT歌词文件 ({encoding}): {file_path}")
                            return content
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    self.logger.warning(f"使用编码 {encoding} 读取失败: {e}")
                    continue
            
            self.logger.error(f"无法读取TXT歌词文件: {file_path}")
            return None
            
        except Exception as e:
            self.logger.error(f"读取TXT歌词文件失败: {e}")
            return None
    
    def write_lyrics(self, file_path: str, lyrics: str, metadata: Dict = None) -> bool:
        """写入TXT歌词文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # 如果有元数据，添加到文件开头
                if metadata:
                    f.write("# 歌词信息\n")
                    for key, value in metadata.items():
                        f.write(f"# {key}: {value}\n")
                    f.write("\n")
                
                f.write(lyrics)
            
            self.logger.info(f"成功写入TXT歌词文件: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"写入TXT歌词文件失败: {e}")
            return False
    
    def get_metadata(self, file_path: str) -> Dict:
        """获取TXT歌词文件的元数据"""
        metadata = {}
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
                
            for line in lines:
                line = line.strip()
                if line.startswith('#') and ':' in line:
                    # 解析元数据行
                    key_value = line[1:].strip()
                    if ':' in key_value:
                        key, value = key_value.split(':', 1)
                        metadata[key.strip()] = value.strip()
                elif not line.startswith('#'):
                    # 遇到非注释行，停止解析元数据
                    break
                    
        except Exception as e:
            self.logger.error(f"获取TXT歌词元数据失败: {e}")
            
        return metadata
