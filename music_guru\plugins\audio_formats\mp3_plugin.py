#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
MP3格式插件

MIT License
Copyright (c) 2025 Music Guru
"""

import os
from typing import Tuple, Optional, Dict, Any

from utils.audio_format_plugin import AudioFormatPlugin

try:
    import librosa
    import librosa.display
    import mutagen
    from mutagen.mp3 import MP3
    from mutagen.id3 import ID3
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    DEPENDENCIES_AVAILABLE = False


class MP3Plugin(AudioFormatPlugin):
    """MP3格式插件"""
    
    def __init__(self):
        """初始化MP3格式插件"""
        super().__init__()
        self.name = "MP3格式"
        self.description = "支持MP3音频格式"
        self.extensions = ["mp3"]
    
    def can_handle(self, file_path: str) -> bool:
        """
        检查是否可以处理指定文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            bool: 是否可以处理
        """
        if not DEPENDENCIES_AVAILABLE:
            return False
        
        ext = self.get_file_extension(file_path)
        if ext not in self.extensions:
            return False
        
        try:
            # 尝试加载文件头
            with open(file_path, 'rb') as f:
                header = f.read(10)
                # 检查MP3文件头标识
                return header.startswith(b'\xFF\xFB') or header.startswith(b'ID3')
        except Exception:
            return False
    
    def load_audio(self, file_path: str) -> Tuple[Optional[Any], Optional[int]]:
        """
        加载音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Tuple[Optional[Any], Optional[int]]: 音频数据和采样率
        """
        if not DEPENDENCIES_AVAILABLE:
            return None, None
        
        try:
            audio_data, sample_rate = librosa.load(file_path, sr=None)
            return audio_data, sample_rate
        except Exception as e:
            print(f"加载MP3文件失败: {e}")
            return None, None
    
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        获取音频元数据
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Dict[str, Any]: 元数据字典
        """
        metadata = {
            "title": "",
            "artist": "",
            "album": "",
            "year": "",
            "genre": "",
            "duration": 0
        }
        
        if not DEPENDENCIES_AVAILABLE:
            return metadata
        
        try:
            audio = MP3(file_path)
            
            # 获取ID3标签
            if audio.tags:
                tags = audio.tags
                
                # 标题
                if "TIT2" in tags:
                    metadata["title"] = str(tags["TIT2"])
                
                # 艺术家
                if "TPE1" in tags:
                    metadata["artist"] = str(tags["TPE1"])
                
                # 专辑
                if "TALB" in tags:
                    metadata["album"] = str(tags["TALB"])
                
                # 年份
                if "TDRC" in tags:
                    metadata["year"] = str(tags["TDRC"])
                
                # 流派
                if "TCON" in tags:
                    metadata["genre"] = str(tags["TCON"])
            
            # 时长
            metadata["duration"] = audio.info.length
            
            return metadata
        except Exception as e:
            print(f"获取MP3元数据失败: {e}")
            return metadata
    
    def get_lyrics(self, file_path: str) -> Optional[str]:
        """
        获取歌词
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Optional[str]: 歌词文本
        """
        if not DEPENDENCIES_AVAILABLE:
            return None
        
        try:
            # 尝试从ID3标签中获取歌词
            tags = ID3(file_path)
            
            # 检查USLT标签（非同步歌词）
            if "USLT" in tags:
                return str(tags["USLT"].text)
            
            # 检查SYLT标签（同步歌词）
            if "SYLT" in tags:
                # 提取纯文本歌词
                sylt = tags["SYLT"]
                lyrics_text = ""
                for time, text in sylt.text:
                    lyrics_text += text + "\n"
                return lyrics_text
            
            # 尝试查找同名LRC文件
            lrc_path = os.path.splitext(file_path)[0] + ".lrc"
            if os.path.exists(lrc_path):
                with open(lrc_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            return None
        except Exception as e:
            print(f"获取MP3歌词失败: {e}")
            return None
