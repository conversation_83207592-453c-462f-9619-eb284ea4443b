#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断评分重复问题的根本原因
"""

import os
import sys
import logging
import json

print("开始诊断评分重复问题...")

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def deep_analyze_scoring_pipeline():
    """深度分析评分流水线"""
    print("\n=== 深度分析评分流水线 ===")
    
    # 设置详细日志
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        from plugins.emotional.lyrics_analysis import LyricsAnalysisPlugin
        from plugins.technical.harmony_progression import HarmonyProgressionPlugin
        from plugins.emotional.emotion_delivery import EmotionDeliveryPlugin
        from core.data_model import MusicData
        
        # 测试多个不同的歌词样本
        test_cases = [
            {
                "name": "简单中文歌词",
                "lyrics": "我爱你\n你爱我\n我们在一起\n很快乐",
                "expected_different": True
            },
            {
                "name": "复杂中文歌词",
                "lyrics": "你像春风一样温暖\n如星光一样闪亮\n仿佛天使在歌唱\n带给我无限希望\n爱情如火般燃烧\n心中的花朵绽放",
                "expected_different": True
            },
            {
                "name": "英文歌词",
                "lyrics": "Love is like a butterfly\nFlying high up in the sky\nBeautiful and free\nDancing gracefully\nIn the moonlight so bright",
                "expected_different": True
            },
            {
                "name": "中英混合歌词",
                "lyrics": "Love is amazing\n你是我心中的star\n在New York的街道上\n我们share着同一个dream\nThis is our story",
                "expected_different": True
            },
            {
                "name": "现代流行歌词",
                "lyrics": "在这个AI的时代\n我们用social media连接\n每天刷着短视频\nemo的时候听音乐\n这很cool这很awesome",
                "expected_different": True
            }
        ]
        
        print(f"\n{'='*80}")
        print("歌词分析插件详细诊断")
        print(f"{'='*80}")
        
        lyrics_plugin = LyricsAnalysisPlugin()
        results = []
        
        for i, case in enumerate(test_cases):
            print(f"\n--- 测试案例 {i+1}: {case['name']} ---")
            
            # 重置插件状态
            lyrics_plugin.reset()
            
            # 创建新的数据对象
            data = MusicData()
            data.title = f"测试歌曲_{i+1}"
            data.lyrics = case['lyrics']
            
            print(f"歌词内容: {repr(case['lyrics'])}")
            print(f"歌词长度: {len(case['lyrics'])}字符")
            
            # 执行评价
            result = lyrics_plugin.evaluate(data)
            score = result.get("score", 0)
            details = result.get("details", {})
            
            print(f"最终得分: {score:.2f}/100")
            print(f"详细评分:")
            for key, detail in details.items():
                print(f"  {key}: {detail}")
            
            # 记录结果用于分析
            results.append({
                "case": case['name'],
                "lyrics": case['lyrics'],
                "score": score,
                "details": details
            })
        
        # 分析结果差异
        print(f"\n{'='*80}")
        print("结果差异分析")
        print(f"{'='*80}")
        
        scores = [r['score'] for r in results]
        unique_scores = set(scores)
        
        print(f"总测试案例: {len(results)}")
        print(f"不同分数: {len(unique_scores)}")
        print(f"分数列表: {scores}")
        print(f"唯一分数: {sorted(unique_scores)}")
        
        if len(unique_scores) == 1:
            print("❌ 严重问题：所有测试案例得到相同分数！")
            analyze_identical_scores(results)
        elif len(unique_scores) < len(results) // 2:
            print("⚠️ 警告：分数差异化不足")
            analyze_insufficient_differentiation(results)
        else:
            print("✅ 分数差异化正常")
        
        return results
        
    except Exception as e:
        print(f"❌ 诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def analyze_identical_scores(results):
    """分析相同分数的原因"""
    print(f"\n--- 相同分数原因分析 ---")
    
    if not results:
        return
    
    # 检查是否所有详细评分都相同
    first_details = results[0]['details']
    all_same_details = True
    
    for result in results[1:]:
        if result['details'] != first_details:
            all_same_details = False
            break
    
    if all_same_details:
        print("❌ 所有详细评分都相同，可能原因：")
        print("  1. 插件使用固定参数而不是实际歌词分析")
        print("  2. 参数计算函数返回固定值")
        print("  3. 数据库加载失败，使用默认值")
        print("  4. 歌词获取失败，使用空字符串")
    else:
        print("ℹ️ 详细评分不同但总分相同，可能原因：")
        print("  1. 评分函数计算逻辑有问题")
        print("  2. 权重分配导致差异被抵消")
        print("  3. 评分范围设置不当")

def analyze_insufficient_differentiation(results):
    """分析分数差异化不足的原因"""
    print(f"\n--- 分数差异化不足原因分析 ---")
    
    # 统计分数分布
    from collections import Counter
    score_counts = Counter([r['score'] for r in results])
    
    print("分数分布:")
    for score, count in sorted(score_counts.items()):
        print(f"  {score:.2f}分: {count}个案例")
    
    # 分析重复分数的案例
    for score, count in score_counts.items():
        if count > 1:
            print(f"\n得分{score:.2f}的案例:")
            same_score_cases = [r for r in results if r['score'] == score]
            for case in same_score_cases:
                print(f"  - {case['case']}: {len(case['lyrics'])}字符")

def test_parameter_calculation():
    """测试参数计算是否正确"""
    print(f"\n{'='*80}")
    print("参数计算测试")
    print(f"{'='*80}")
    
    try:
        from plugins.emotional.lyrics_analysis import LyricsAnalysisPlugin
        
        plugin = LyricsAnalysisPlugin()
        
        # 测试不同复杂度的歌词
        test_lyrics = [
            ("简单", "我爱你"),
            ("中等", "你像春风一样温暖，如星光一样闪亮"),
            ("复杂", "你像春风一样温暖，如星光一样闪亮，仿佛天使在歌唱，爱情如火般燃烧，心中的花朵绽放，时光如水般流淌"),
            ("英文", "Love is like a butterfly flying high"),
            ("混合", "Love is amazing 你是我的star in New York we share dreams")
        ]
        
        print("\n参数计算对比:")
        print(f"{'歌词类型':<10} {'词汇丰富度':<12} {'押韵复杂度':<12} {'隐喻密度':<12} {'国际传播':<12}")
        print("-" * 70)
        
        for name, lyrics in test_lyrics:
            lines = lyrics.split('\n') if '\n' in lyrics else [lyrics]
            words = lyrics.split()
            unique_words = set(word.lower().strip('.,!?;:"()[]') for word in words if word.strip())
            
            # 计算各项参数
            vocab_richness = len(unique_words) / len(words) if len(words) > 0 else 0
            rhyme_complexity = plugin.calculate_rhyme_scheme_advanced(lines)
            metaphor_density = plugin.calculate_metaphor_density_advanced(lyrics)
            intl_appeal = plugin.calculate_international_appeal_advanced(lyrics, words)
            
            print(f"{name:<10} {vocab_richness:<12.4f} {rhyme_complexity:<12.4f} {metaphor_density:<12.4f} {intl_appeal:<12.4f}")
        
    except Exception as e:
        print(f"❌ 参数计算测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_database_loading():
    """测试数据库加载"""
    print(f"\n{'='*80}")
    print("数据库加载测试")
    print(f"{'='*80}")
    
    try:
        from plugins.emotional.lyrics_analysis import LyricsAnalysisPlugin
        
        plugin = LyricsAnalysisPlugin()
        
        databases = [
            'metaphor_patterns.json',
            'international_elements.json',
            'rhyme_patterns.json',
            'trending_words_2024.json'
        ]
        
        for db_name in databases:
            print(f"\n测试数据库: {db_name}")
            db_data = plugin.load_database(db_name)
            
            if db_data:
                print(f"✅ 加载成功，包含 {len(db_data)} 个主要类别")
                # 显示一些示例数据
                for key, value in list(db_data.items())[:2]:
                    if isinstance(value, dict):
                        print(f"  {key}: {len(value)} 个子类别")
                    elif isinstance(value, list):
                        print(f"  {key}: {len(value)} 个项目")
                    else:
                        print(f"  {key}: {type(value)}")
            else:
                print(f"❌ 加载失败或为空")
        
    except Exception as e:
        print(f"❌ 数据库加载测试失败: {e}")
        import traceback
        traceback.print_exc()

def identify_root_causes():
    """识别根本原因"""
    print(f"\n{'='*80}")
    print("根本原因识别")
    print(f"{'='*80}")
    
    print("\n🔍 可能的根本原因:")
    print("1. 参数获取问题:")
    print("   - 歌词获取失败，始终为空字符串")
    print("   - 参数计算函数有bug，返回固定值")
    print("   - 数据库加载失败，使用默认值")
    
    print("\n2. 评分计算问题:")
    print("   - 评分函数逻辑错误")
    print("   - 权重设置不当")
    print("   - 分数范围设置过窄")
    
    print("\n3. 状态管理问题:")
    print("   - 插件状态没有完全重置")
    print("   - 缓存数据影响新计算")
    print("   - 全局变量或类变量污染")
    
    print("\n4. 数据流问题:")
    print("   - 数据传递过程中丢失")
    print("   - 对象引用问题")
    print("   - 深拷贝vs浅拷贝问题")

if __name__ == "__main__":
    print("开始评分重复问题诊断...")
    
    # 深度分析评分流水线
    results = deep_analyze_scoring_pipeline()
    
    # 测试参数计算
    test_parameter_calculation()
    
    # 测试数据库加载
    test_database_loading()
    
    # 识别根本原因
    identify_root_causes()
    
    print(f"\n{'='*80}")
    print("诊断完成")
    print(f"{'='*80}")
    
    print("\n📋 下一步行动:")
    print("1. 运行此诊断脚本，查看具体问题")
    print("2. 根据诊断结果修复相应问题")
    print("3. 重新测试确认修复效果")
    print("4. 实现网络歌词搜索功能")
