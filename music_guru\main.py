#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
主程序入口

MIT License
Copyright (c) 2025 Music Guru
"""

import sys
import os
import logging
from PyQt6.QtWidgets import QApplication
from gui.main_window import MainWindow
from core.plugin_manager import PluginManager
from core.evaluator import Evaluator
from config.settings import Settings
# 这些导入在 MainWindow 类中已经处理，这里不需要重复导入
# from utils.audio_format_plugin import AudioFormatManager
# from plugins.audio_formats.enhanced_mp3_plugin import EnhancedMP3Plugin
# from plugins.audio_formats.enhanced_flac_plugin import EnhancedFLACPlugin
# from plugins.audio_formats.universal_audio_plugin import UniversalAudioPlugin


def main():
    """主程序入口函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler(os.path.join(os.path.dirname(__file__), 'music_guru.log'), encoding='utf-8')
        ]
    )

    # 确保数据目录存在
    data_dir = os.path.join(os.path.dirname(__file__), 'data')
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    # 初始化应用
    app = QApplication(sys.argv)
    app.setApplicationName("Music Guru")
    app.setApplicationVersion("1.0.0")

    # 加载设置
    settings = Settings()

    # 初始化插件管理器
    plugin_manager = PluginManager()
    plugin_manager.load_all_plugins()

    # 初始化评价引擎
    evaluator = Evaluator(plugin_manager, settings)

    # 创建主窗口
    main_window = MainWindow(plugin_manager, evaluator, settings)
    main_window.show()

    # 运行应用
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
