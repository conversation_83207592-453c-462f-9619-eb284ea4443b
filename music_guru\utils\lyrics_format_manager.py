#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
歌词格式管理器

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Tuple


class LyricsFormatPlugin(ABC):
    """歌词格式插件基类"""
    
    def __init__(self):
        self.name = ""
        self.description = ""
        self.file_extensions = []  # 支持的文件扩展名
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def can_handle(self, file_path: str) -> bool:
        """检查是否能处理指定文件"""
        pass
    
    @abstractmethod
    def read_lyrics(self, file_path: str) -> Optional[str]:
        """读取歌词文件"""
        pass
    
    @abstractmethod
    def write_lyrics(self, file_path: str, lyrics: str, metadata: Dict = None) -> bool:
        """写入歌词文件"""
        pass
    
    def get_metadata(self, file_path: str) -> Dict:
        """获取歌词文件的元数据"""
        return {}


class LyricsFormatManager:
    """歌词格式管理器"""
    
    def __init__(self):
        self.plugins = {}
        self.logger = logging.getLogger("LyricsFormatManager")
        
        # 注册默认插件
        self._register_default_plugins()
    
    def _register_default_plugins(self):
        """注册默认插件"""
        from utils.lyrics_formats.txt_plugin import TxtLyricsPlugin
        from utils.lyrics_formats.lrc_plugin import LrcLyricsPlugin
        from utils.lyrics_formats.srt_plugin import SrtLyricsPlugin
        from utils.lyrics_formats.json_plugin import JsonLyricsPlugin
        
        plugins = [
            TxtLyricsPlugin(),
            LrcLyricsPlugin(),
            SrtLyricsPlugin(),
            JsonLyricsPlugin()
        ]
        
        for plugin in plugins:
            self.register_plugin(plugin)
    
    def register_plugin(self, plugin: LyricsFormatPlugin):
        """注册歌词格式插件"""
        plugin_id = plugin.__class__.__name__
        self.plugins[plugin_id] = plugin
        self.logger.info(f"注册歌词格式插件: {plugin_id} ({plugin.name})")
    
    def get_plugin_for_file(self, file_path: str) -> Optional[LyricsFormatPlugin]:
        """获取适合处理指定文件的插件"""
        for plugin in self.plugins.values():
            if plugin.can_handle(file_path):
                return plugin
        return None
    
    def find_lyrics_files(self, music_file_path: str) -> List[Tuple[str, LyricsFormatPlugin]]:
        """查找音乐文件对应的歌词文件"""
        base_path = os.path.splitext(music_file_path)[0]
        base_dir = os.path.dirname(music_file_path)
        base_name = os.path.basename(base_path)
        
        found_files = []
        
        # 获取所有支持的扩展名
        all_extensions = set()
        for plugin in self.plugins.values():
            all_extensions.update(plugin.file_extensions)
        
        # 查找同名歌词文件
        for ext in all_extensions:
            lyrics_path = base_path + ext
            if os.path.exists(lyrics_path):
                plugin = self.get_plugin_for_file(lyrics_path)
                if plugin:
                    found_files.append((lyrics_path, plugin))
                    self.logger.info(f"找到歌词文件: {lyrics_path}")
        
        # 查找可能的变体名称
        possible_names = [
            base_name,
            base_name.lower(),
            base_name.upper(),
            base_name.replace(" ", "_"),
            base_name.replace("_", " ")
        ]
        
        for name in possible_names:
            if name != base_name:  # 避免重复查找
                for ext in all_extensions:
                    lyrics_path = os.path.join(base_dir, name + ext)
                    if os.path.exists(lyrics_path):
                        plugin = self.get_plugin_for_file(lyrics_path)
                        if plugin and (lyrics_path, plugin) not in found_files:
                            found_files.append((lyrics_path, plugin))
                            self.logger.info(f"找到变体歌词文件: {lyrics_path}")
        
        return found_files
    
    def read_lyrics(self, file_path: str) -> Optional[str]:
        """读取歌词文件"""
        plugin = self.get_plugin_for_file(file_path)
        if plugin:
            try:
                return plugin.read_lyrics(file_path)
            except Exception as e:
                self.logger.error(f"读取歌词文件失败 {file_path}: {e}")
        return None
    
    def write_lyrics(self, file_path: str, lyrics: str, format_type: str = "txt", metadata: Dict = None) -> bool:
        """写入歌词文件"""
        # 根据格式类型选择插件
        plugin = None
        for p in self.plugins.values():
            if format_type.lower() in [ext.lower().lstrip('.') for ext in p.file_extensions]:
                plugin = p
                break
        
        if plugin:
            try:
                return plugin.write_lyrics(file_path, lyrics, metadata)
            except Exception as e:
                self.logger.error(f"写入歌词文件失败 {file_path}: {e}")
        return False
    
    def get_supported_formats(self) -> Dict[str, str]:
        """获取支持的格式列表"""
        formats = {}
        for plugin in self.plugins.values():
            for ext in plugin.file_extensions:
                formats[ext] = plugin.name
        return formats
