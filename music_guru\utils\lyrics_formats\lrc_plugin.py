#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
LRC歌词格式插件

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import re
from typing import Dict, Optional
from utils.lyrics_format_manager import LyricsFormatPlugin


class LrcLyricsPlugin(LyricsFormatPlugin):
    """LRC歌词格式插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "LRC时间轴歌词"
        self.description = "支持LRC格式的时间轴歌词文件"
        self.file_extensions = [".lrc"]
    
    def can_handle(self, file_path: str) -> bool:
        """检查是否能处理指定文件"""
        ext = os.path.splitext(file_path)[1].lower()
        return ext in self.file_extensions
    
    def read_lyrics(self, file_path: str) -> Optional[str]:
        """读取LRC歌词文件"""
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin1']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read().strip()
                        if content:
                            # 解析LRC格式，提取纯歌词
                            lyrics = self._parse_lrc_content(content)
                            if lyrics:
                                self.logger.info(f"成功读取LRC歌词文件 ({encoding}): {file_path}")
                                return lyrics
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    self.logger.warning(f"使用编码 {encoding} 读取失败: {e}")
                    continue
            
            self.logger.error(f"无法读取LRC歌词文件: {file_path}")
            return None
            
        except Exception as e:
            self.logger.error(f"读取LRC歌词文件失败: {e}")
            return None
    
    def _parse_lrc_content(self, content: str) -> str:
        """解析LRC内容，提取纯歌词"""
        lines = content.split('\n')
        lyrics_lines = []
        
        # LRC时间标签的正则表达式
        time_pattern = re.compile(r'\[(\d{2}):(\d{2})\.(\d{2,3})\]')
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检查是否是元数据行
            if line.startswith('[') and not time_pattern.match(line):
                continue
            
            # 移除时间标签
            lyrics_line = time_pattern.sub('', line).strip()
            if lyrics_line:
                lyrics_lines.append(lyrics_line)
        
        return '\n'.join(lyrics_lines)
    
    def write_lyrics(self, file_path: str, lyrics: str, metadata: Dict = None) -> bool:
        """写入LRC歌词文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                # 写入元数据
                if metadata:
                    for key, value in metadata.items():
                        f.write(f"[{key}:{value}]\n")
                    f.write("\n")
                
                # 写入歌词（不带时间轴）
                lines = lyrics.split('\n')
                for i, line in enumerate(lines):
                    if line.strip():
                        # 生成简单的时间轴（每行间隔4秒）
                        minutes = (i * 4) // 60
                        seconds = (i * 4) % 60
                        f.write(f"[{minutes:02d}:{seconds:02d}.00]{line}\n")
            
            self.logger.info(f"成功写入LRC歌词文件: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"写入LRC歌词文件失败: {e}")
            return False
    
    def get_metadata(self, file_path: str) -> Dict:
        """获取LRC歌词文件的元数据"""
        metadata = {}
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 解析LRC元数据标签
            metadata_pattern = re.compile(r'\[([a-zA-Z]+):([^\]]+)\]')
            matches = metadata_pattern.findall(content)
            
            for key, value in matches:
                metadata[key.lower()] = value.strip()
                    
        except Exception as e:
            self.logger.error(f"获取LRC歌词元数据失败: {e}")
            
        return metadata
