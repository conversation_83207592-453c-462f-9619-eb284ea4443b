#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试评分追踪功能
"""

import os
import sys
import logging

print("开始测试评分追踪功能...")

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_lyrics_scoring_trace():
    """测试歌词分析评分追踪"""
    print("\n=== 测试歌词分析评分追踪 ===")
    
    # 设置详细日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        from plugins.emotional.lyrics_analysis import LyricsAnalysisPlugin
        from core.data_model import MusicData
        
        plugin = LyricsAnalysisPlugin()
        
        # 测试不同复杂度的歌词
        test_cases = [
            {
                "title": "简单歌词",
                "lyrics": """
                我爱你
                你爱我
                我们在一起
                很快乐
                """
            },
            {
                "title": "中等复杂度歌词",
                "lyrics": """
                你像春风一样温暖
                如星光一样闪亮
                仿佛天使在歌唱
                带给我无限希望
                Love is like a butterfly
                Flying high up in the sky
                Beautiful and free
                Dancing gracefully
                """
            },
            {
                "title": "复杂歌词",
                "lyrics": """
                你的眼眸如深邃的海洋
                波光粼粼诉说着忧伤
                像夜空中最亮的星
                似梦境里最美的光
                仿佛听见天使的歌声
                宛如置身仙境的花香
                Love flows like a river
                Through valleys deep and wide
                Your voice seems like thunder
                Rolling across the mountainside
                Beautiful melodies dancing
                In the moonlight so bright
                """
            }
        ]
        
        print(f"\n{'='*60}")
        print("歌词分析评分追踪结果")
        print(f"{'='*60}")
        
        for i, case in enumerate(test_cases):
            print(f"\n--- 测试案例 {i+1}: {case['title']} ---")
            
            # 创建音乐数据
            data = MusicData()
            data.title = case['title']
            data.lyrics = case['lyrics'].strip()
            
            # 评价
            result = plugin.evaluate(data)
            score = result.get("score", 0)
            details = result.get("details", {})
            
            print(f"最终得分: {score:.1f}/100")
            print(f"详细评分:")
            for key, detail in details.items():
                print(f"  {key}: {detail}")
        
        print(f"\n✅ 歌词分析评分追踪测试完成")
        
    except Exception as e:
        print(f"❌ 歌词分析评分追踪测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_harmony_scoring_trace():
    """测试和声进行评分追踪"""
    print("\n=== 测试和声进行评分追踪 ===")
    
    try:
        from plugins.technical.harmony_progression import HarmonyProgressionPlugin
        from core.data_model import MusicData
        
        plugin = HarmonyProgressionPlugin()
        
        # 测试不同和声复杂度
        test_cases = [
            {
                "title": "创新和声",
                "formula_usage": 0.25,  # 低套用率
                "key_changes": 2,       # 多次转调
                "alt_chord_ratio": 0.35 # 高替代和弦使用率
            },
            {
                "title": "一般和声",
                "formula_usage": 0.55,  # 中等套用率
                "key_changes": 1,       # 一次转调
                "alt_chord_ratio": 0.15 # 中等替代和弦使用率
            },
            {
                "title": "套路和声",
                "formula_usage": 0.85,  # 高套用率
                "key_changes": 0,       # 无转调
                "alt_chord_ratio": 0.05 # 低替代和弦使用率
            }
        ]
        
        print(f"\n{'='*60}")
        print("和声进行评分追踪结果")
        print(f"{'='*60}")
        
        for i, case in enumerate(test_cases):
            print(f"\n--- 测试案例 {i+1}: {case['title']} ---")
            
            # 创建音乐数据
            data = MusicData()
            data.title = case['title']
            
            # 设置和声参数
            data.technical_data["harmony_progression"]["formula_usage"] = case["formula_usage"]
            data.technical_data["harmony_progression"]["key_changes"] = case["key_changes"]
            data.technical_data["harmony_progression"]["alt_chord_ratio"] = case["alt_chord_ratio"]
            
            # 评价
            result = plugin.evaluate(data)
            score = result.get("score", 0)
            details = result.get("details", {})
            
            print(f"最终得分: {score:.1f}/100")
            print(f"详细评分:")
            for key, detail in details.items():
                print(f"  {key}: {detail}")
        
        print(f"\n✅ 和声进行评分追踪测试完成")
        
    except Exception as e:
        print(f"❌ 和声进行评分追踪测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_emotion_scoring_trace():
    """测试情绪传达评分追踪"""
    print("\n=== 测试情绪传达评分追踪 ===")
    
    try:
        from plugins.emotional.emotion_delivery import EmotionDeliveryPlugin
        from core.data_model import MusicData
        
        plugin = EmotionDeliveryPlugin()
        
        # 测试不同情绪强度
        test_cases = [
            {
                "title": "高情绪强度",
                "chorus_range": 13.5,   # 大音域
                "dynamic_range": 11.2,  # 大动态范围
                "synesthesia_count": 6  # 多通感修辞
            },
            {
                "title": "中等情绪强度",
                "chorus_range": 8.5,    # 中等音域
                "dynamic_range": 7.8,   # 中等动态范围
                "synesthesia_count": 3  # 中等通感修辞
            },
            {
                "title": "低情绪强度",
                "chorus_range": 4.2,    # 小音域
                "dynamic_range": 4.5,   # 小动态范围
                "synesthesia_count": 1  # 少通感修辞
            }
        ]
        
        print(f"\n{'='*60}")
        print("情绪传达评分追踪结果")
        print(f"{'='*60}")
        
        for i, case in enumerate(test_cases):
            print(f"\n--- 测试案例 {i+1}: {case['title']} ---")
            
            # 创建音乐数据
            data = MusicData()
            data.title = case['title']
            
            # 设置情绪参数
            data.emotional_data["emotion_delivery"]["chorus_range"] = case["chorus_range"]
            data.production_data["recording_quality"]["dynamic_range"] = case["dynamic_range"]
            data.emotional_data["emotion_delivery"]["synesthesia_count"] = case["synesthesia_count"]
            
            # 评价
            result = plugin.evaluate(data)
            score = result.get("score", 0)
            details = result.get("details", {})
            
            print(f"最终得分: {score:.1f}/100")
            print(f"详细评分:")
            for key, detail in details.items():
                print(f"  {key}: {detail}")
        
        print(f"\n✅ 情绪传达评分追踪测试完成")
        
    except Exception as e:
        print(f"❌ 情绪传达评分追踪测试失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_scoring_differences():
    """分析评分差异"""
    print(f"\n{'='*60}")
    print("评分差异分析")
    print(f"{'='*60}")
    
    print("\n📊 预期评分差异:")
    print("1. 歌词分析:")
    print("   - 简单歌词: 约20-30分 (低词汇丰富度、无隐喻)")
    print("   - 中等歌词: 约50-65分 (中等复杂度、有英文)")
    print("   - 复杂歌词: 约75-90分 (高词汇丰富度、丰富修辞)")
    
    print("\n2. 和声进行:")
    print("   - 创新和声: 约85-95分 (低套用率、多转调)")
    print("   - 一般和声: 约60-75分 (中等参数)")
    print("   - 套路和声: 约25-40分 (高套用率、无创新)")
    
    print("\n3. 情绪传达:")
    print("   - 高情绪: 约85-95分 (大音域、大动态)")
    print("   - 中等情绪: 约55-70分 (中等参数)")
    print("   - 低情绪: 约20-35分 (小音域、小动态)")
    
    print("\n🎯 关键改进:")
    print("✅ 使用连续评分函数替代阶梯函数")
    print("✅ 详细的日志追踪每个评分步骤")
    print("✅ 参数验证和异常检测")
    print("✅ 基于实际音频特征的差异化评分")

if __name__ == "__main__":
    print("开始评分追踪功能验证...")
    
    # 测试各个评分组件
    test_lyrics_scoring_trace()
    test_harmony_scoring_trace()
    test_emotion_scoring_trace()
    
    # 分析评分差异
    analyze_scoring_differences()
    
    print(f"\n{'='*60}")
    print("评分追踪测试完成")
    print(f"{'='*60}")
    
    print("\n📋 修复总结:")
    print("1. ✅ 歌词分析插件：实现基于实际歌词的差异化评分")
    print("2. ✅ 和声进行插件：实现基于音频分析的连续评分")
    print("3. ✅ 情绪传达插件：实现基于情感参数的精确评分")
    print("4. ✅ 详细日志追踪：每个评分步骤都有完整记录")
    print("5. ✅ 参数验证：检测固定分数和异常情况")
    
    print("\n🎯 下一步:")
    print("1. 重启Music Guru程序以加载新的评分逻辑")
    print("2. 运行实际音乐文件测试，验证评分差异化")
    print("3. 检查日志确认评分计算过程正确")
    print("4. 根据需要微调评分参数和阈值")
