#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
歌词分析工具

MIT License
Copyright (c) 2025 Music Guru
"""

import re
import os
from typing import Dict, List, Tuple, Optional
import json


class LyricsAnalyzer:
    """歌词分析工具类"""
    
    def __init__(self):
        """初始化歌词分析器"""
        self.lyrics_text = ""
        self.lines = []
        self.word_count = 0
        
        # 加载热词库
        self.trending_words = self._load_trending_words()
        
        # 加载通感修辞词库
        self.synesthesia_words = self._load_synesthesia_words()
    
    def load_lyrics(self, lyrics_text: str) -> bool:
        """
        加载歌词文本
        
        Args:
            lyrics_text: 歌词文本
            
        Returns:
            bool: 是否成功加载
        """
        if not lyrics_text:
            return False
        
        self.lyrics_text = lyrics_text
        
        # 处理LRC格式
        if '[' in lyrics_text and ']' in lyrics_text:
            # 移除时间标签
            self.lines = []
            for line in lyrics_text.split('\n'):
                # 移除时间标签 [mm:ss.xx]
                clean_line = re.sub(r'\[\d+:\d+\.\d+\]', '', line).strip()
                if clean_line:
                    self.lines.append(clean_line)
        else:
            # 普通文本格式
            self.lines = [line.strip() for line in lyrics_text.split('\n') if line.strip()]
        
        # 计算词数
        self.word_count = sum(len(re.findall(r'\b\w+\b', line)) for line in self.lines)
        
        return True
    
    def _load_trending_words(self) -> List[str]:
        """
        加载热词库
        
        Returns:
            List[str]: 热词列表
        """
        try:
            # 尝试从文件加载热词库
            file_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'trending_words.json')
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            # 如果文件不存在，返回默认热词
            return [
                "元宇宙", "内卷", "躺平", "破防", "绝绝子", "yyds", "塌房", "双减",
                "破圈", "顶流", "凡尔赛", "打工人", "后浪", "脱口秀", "直播带货", "云监工",
                "硬核", "真香", "996", "00后", "Z世代", "国潮", "盲盒", "断舍离",
                "佛系", "养生", "宅家", "云旅游", "云蹦迪", "云监工", "云录制", "云追星"
            ]
        except Exception as e:
            print(f"加载热词库失败: {e}")
            return []
    
    def _load_synesthesia_words(self) -> List[str]:
        """
        加载通感修辞词库
        
        Returns:
            List[str]: 通感修辞词列表
        """
        try:
            # 尝试从文件加载通感修辞词库
            file_path = os.path.join(os.path.dirname(__file__), '..', 'data', 'synesthesia_words.json')
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
            # 如果文件不存在，返回默认通感修辞词
            return [
                "甜蜜的声音", "刺耳的色彩", "冰冷的目光", "温暖的话语", "苦涩的记忆",
                "明亮的旋律", "沉重的气氛", "轻柔的色调", "尖锐的香味", "柔软的光线",
                "冷酷的音符", "火热的眼神", "黑暗的声音", "刺眼的噪音", "沙哑的色彩",
                "浓烈的旋律", "清脆的触感", "模糊的声音", "锋利的气息", "厚重的色彩"
            ]
        except Exception as e:
            print(f"加载通感修辞词库失败: {e}")
            return []
    
    def analyze_trending_words_ratio(self) -> float:
        """
        分析年度热词覆盖率
        
        Returns:
            float: 热词覆盖率（0-1）
        """
        if not self.lyrics_text or not self.trending_words:
            return 0
        
        # 统计热词出现次数
        count = 0
        for word in self.trending_words:
            count += self.lyrics_text.count(word)
        
        # 计算覆盖率
        if self.word_count > 0:
            return min(1.0, count / self.word_count)
        return 0
    
    def analyze_synesthesia_count(self) -> int:
        """
        分析通感修辞数量
        
        Returns:
            int: 通感修辞数量
        """
        if not self.lyrics_text or not self.synesthesia_words:
            return 0
        
        # 统计通感修辞出现次数
        count = 0
        for phrase in self.synesthesia_words:
            count += self.lyrics_text.count(phrase)
        
        return count
    
    def analyze_scene_density(self) -> float:
        """
        分析具象场景描写密度
        
        Returns:
            float: 具象场景描写密度（每分钟场景数）
        """
        if not self.lines:
            return 0
        
        # 场景标记词
        scene_markers = [
            "看见", "听到", "感觉", "闻到", "触摸", "站在", "走在", "坐在",
            "躺在", "房间", "街道", "城市", "天空", "海边", "山上", "树下",
            "窗前", "门口", "床上", "车里", "雨中", "阳光", "月光", "星空"
        ]
        
        # 统计场景描写数量
        scene_count = 0
        for line in self.lines:
            for marker in scene_markers:
                if marker in line:
                    scene_count += 1
                    break
        
        # 假设平均每首歌3分钟
        return scene_count / 3
    
    def analyze_lyrics_clarity(self) -> float:
        """
        分析歌词语义模糊度
        
        Returns:
            float: 歌词语义模糊度（0-1，越低越清晰）
        """
        if not self.lines:
            return 0
        
        # 模糊词标记
        ambiguous_markers = [
            "也许", "可能", "或许", "大概", "似乎", "好像", "仿佛", "宛如",
            "恍惚", "朦胧", "迷离", "恍惚", "若隐若现", "模糊", "不确定",
            "不知道", "不明白", "不理解", "不清楚", "不确定", "不明确"
        ]
        
        # 统计模糊词数量
        ambiguous_count = 0
        for line in self.lines:
            for marker in ambiguous_markers:
                ambiguous_count += line.count(marker)
        
        # 计算模糊度
        if self.word_count > 0:
            return min(1.0, ambiguous_count / self.word_count)
        return 0
    
    def analyze_plot_structure(self) -> bool:
        """
        分析情感转折点是否符合三幕剧结构
        
        Returns:
            bool: 是否符合三幕剧结构
        """
        if len(self.lines) < 10:
            return False
        
        # 情感词典
        positive_emotions = [
            "快乐", "幸福", "喜悦", "兴奋", "激动", "欢笑", "开心", "愉悦",
            "满足", "欣慰", "感动", "温暖", "希望", "憧憬", "向往", "美好"
        ]
        
        negative_emotions = [
            "悲伤", "痛苦", "难过", "伤心", "失落", "绝望", "沮丧", "忧郁",
            "孤独", "寂寞", "无奈", "遗憾", "后悔", "愤怒", "恐惧", "焦虑"
        ]
        
        # 将歌词分为三部分
        part_size = len(self.lines) // 3
        part1 = self.lines[:part_size]
        part2 = self.lines[part_size:2*part_size]
        part3 = self.lines[2*part_size:]
        
        # 分析每部分的情感倾向
        def analyze_emotion(lines):
            positive_count = 0
            negative_count = 0
            for line in lines:
                for word in positive_emotions:
                    positive_count += line.count(word)
                for word in negative_emotions:
                    negative_count += line.count(word)
            return positive_count, negative_count
        
        p1_pos, p1_neg = analyze_emotion(part1)
        p2_pos, p2_neg = analyze_emotion(part2)
        p3_pos, p3_neg = analyze_emotion(part3)
        
        # 判断是否符合三幕剧结构（情感有明显转折）
        # 第一幕：设定 -> 第二幕：冲突 -> 第三幕：解决
        
        # 情感转折模式1：正->负->正
        pattern1 = (p1_pos > p1_neg) and (p2_pos < p2_neg) and (p3_pos > p3_neg)
        
        # 情感转折模式2：负->正->负
        pattern2 = (p1_pos < p1_neg) and (p2_pos > p2_neg) and (p3_pos < p3_neg)
        
        # 情感转折模式3：负->负(加强)->正
        pattern3 = (p1_pos < p1_neg) and (p2_pos < p2_neg and p2_neg > p1_neg) and (p3_pos > p3_neg)
        
        return pattern1 or pattern2 or pattern3
