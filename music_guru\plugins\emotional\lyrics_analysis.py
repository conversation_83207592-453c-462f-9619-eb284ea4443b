#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
歌词分析插件

MIT License
Copyright (c) 2025 Music Guru
"""

import logging
from core.plugin_manager import Plugin
from utils.lyrics_analyzer import LyricsAnalyzer


class LyricsAnalysisPlugin(Plugin):
    """歌词分析插件"""

    def __init__(self):
        super().__init__()
        self.name = "歌词分析"
        self.description = "评价歌词质量（叙事结构/语言表达）"
        self.dimension = "emotional"
        self.category = "lyrics_analysis"

        # 参数设置
        self.parameters = {
            "lyrics_text": "",  # 歌词文本
            "rhyme_scheme": 0,  # 押韵方案复杂度
            "metaphor_density": 0,  # 隐喻密度
            "vocabulary_richness": 0,  # 词汇丰富度
            "international_appeal": 0  # 国际传播潜力
        }

        # 创建歌词分析器
        self.analyzer = LyricsAnalyzer()

        # 创建日志记录器
        self.logger = logging.getLogger("LyricsAnalysisPlugin")

    def evaluate(self, data):
        """
        评价歌词质量

        评分标准：
        - 押韵方案复杂度＞0.7（+3分）
        - 隐喻密度＞每8行1个（+2分）
        - 词汇丰富度＞0.6（+2分）
        - 国际传播潜力＞0.5（+3分）

        满分：10分
        """
        self.logger.info(f"开始评价歌词质量: {data.title}")

        score = 0
        details = {}

        # 获取参数 - 优先从实际歌词数据获取
        lyrics_text = getattr(data, 'lyrics', '') or self.parameters.get("lyrics_text", "")
        rhyme_scheme = self.parameters.get("rhyme_scheme", 0)
        metaphor_density = self.parameters.get("metaphor_density", 0)
        vocabulary_richness = self.parameters.get("vocabulary_richness", 0)
        international_appeal = self.parameters.get("international_appeal", 0)

        # 如果有实际歌词，重新计算参数
        if lyrics_text and len(lyrics_text) > 50:  # 确保有足够的歌词内容
            self.logger.info("检测到实际歌词，重新计算参数")

            # 计算歌词的基本统计信息
            lines = lyrics_text.split('\n')
            words = lyrics_text.split()
            unique_words = set(word.lower().strip('.,!?;:"()[]') for word in words if word.strip())

            # 重新计算词汇丰富度 (Type-Token Ratio)
            if len(words) > 0:
                vocabulary_richness = len(unique_words) / len(words)
                self.logger.info(f"重新计算词汇丰富度: {vocabulary_richness:.3f}")

            # 重新计算押韵方案复杂度
            rhyme_scheme = self.calculate_rhyme_scheme(lines)
            self.logger.info(f"重新计算押韵复杂度: {rhyme_scheme:.3f}")

            # 重新计算隐喻密度
            metaphor_density = self.calculate_metaphor_density(lyrics_text)
            self.logger.info(f"重新计算隐喻密度: {metaphor_density:.3f}")

            # 重新计算国际传播潜力（基于英文词汇比例）
            english_words = sum(1 for word in words if word.isascii() and word.isalpha())
            international_appeal = english_words / len(words) if len(words) > 0 else 0
            self.logger.info(f"重新计算国际传播潜力: {international_appeal:.3f}")

        self.logger.info(f"歌词文本长度: {len(lyrics_text) if lyrics_text else 0}")
        self.logger.debug(f"参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                         f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")

        # 加载歌词
        if lyrics_text:
            self.logger.info("加载歌词文本")
            loaded = self.analyzer.load_lyrics(lyrics_text)
            self.logger.info(f"歌词加载{'成功' if loaded else '失败'}")

            if loaded:
                self.logger.info(f"歌词行数: {len(self.analyzer.lines)}")
                self.logger.info(f"歌词词数: {self.analyzer.word_count}")

                # 尝试分析歌词特征
                try:
                    # 更新参数
                    trending_words_ratio = self.analyzer.analyze_trending_words_ratio()
                    self.logger.info(f"年度热词覆盖率: {trending_words_ratio:.4f}")

                    synesthesia_count = self.analyzer.analyze_synesthesia_count()
                    self.logger.info(f"通感修辞数量: {synesthesia_count}")

                    scene_density = self.analyzer.analyze_scene_density()
                    self.logger.info(f"具象场景描写密度: {scene_density:.4f}")

                    lyrics_clarity = self.analyzer.analyze_lyrics_clarity()
                    self.logger.info(f"歌词语义模糊度: {lyrics_clarity:.4f}")

                    plot_structure = self.analyzer.analyze_plot_structure()
                    self.logger.info(f"情感转折点符合三幕剧结构: {plot_structure}")

                    # 更新参数
                    rhyme_scheme = max(rhyme_scheme, trending_words_ratio)
                    metaphor_density = max(metaphor_density, synesthesia_count / max(len(self.analyzer.lines), 1))
                    vocabulary_richness = max(vocabulary_richness, 1 - lyrics_clarity)
                    international_appeal = max(international_appeal, scene_density / 10)

                    self.logger.info(f"更新后的参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                                    f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")
                except Exception as e:
                    self.logger.error(f"分析歌词特征失败: {e}", exc_info=True)
        else:
            self.logger.warning("没有歌词文本")

            # 如果没有歌词，尝试从音频特征推断一些参数
            # 这是一个启发式方法，可以根据实际情况调整
            try:
                # 检查音乐数据中是否有情感相关的数据
                if hasattr(data, 'emotional_data'):
                    # 从情感数据中提取一些参数
                    emotion_delivery = data.emotional_data.get("emotion_delivery", {})
                    narrative_tension = data.emotional_data.get("narrative_tension", {})
                    era_resonance = data.emotional_data.get("era_resonance", {})

                    # 从音频特征中提取一些参数
                    chorus_range = emotion_delivery.get("chorus_range", 0)
                    synesthesia_count = emotion_delivery.get("synesthesia_count", 0)
                    scene_density = narrative_tension.get("scene_density", 0)
                    lyrics_clarity = narrative_tension.get("lyrics_clarity", 0)
                    trending_words_ratio = era_resonance.get("trending_words_ratio", 0)

                    # 更新参数
                    if chorus_range > 0:
                        rhyme_scheme = max(rhyme_scheme, chorus_range / 10)
                    if synesthesia_count > 0:
                        metaphor_density = max(metaphor_density, synesthesia_count / 10)
                    if scene_density > 0:
                        international_appeal = max(international_appeal, scene_density / 10)
                    if lyrics_clarity > 0:
                        vocabulary_richness = max(vocabulary_richness, 1 - lyrics_clarity)
                    if trending_words_ratio > 0:
                        rhyme_scheme = max(rhyme_scheme, trending_words_ratio)

                    self.logger.info(f"从音频特征推断的参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                                    f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")
            except Exception as e:
                self.logger.error(f"从音频特征推断参数失败: {e}", exc_info=True)

            # 如果参数仍然为0，设置一些默认值
            if rhyme_scheme == 0:
                rhyme_scheme = 0.3  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认押韵方案复杂度: {rhyme_scheme}")

            if metaphor_density == 0:
                metaphor_density = 0.05  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认隐喻密度: {metaphor_density}")

            if vocabulary_richness == 0:
                vocabulary_richness = 0.4  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认词汇丰富度: {vocabulary_richness}")

            if international_appeal == 0:
                international_appeal = 0.2  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认国际传播潜力: {international_appeal}")

            self.logger.info(f"最终参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                            f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")

        # 评分计算（参考大模型评分标准）
        self.logger.info("开始评分计算")
        self.logger.info(f"=== 歌词分析详细评分过程 ===")
        self.logger.info(f"歌词文件: {data.title}")
        self.logger.info(f"歌词长度: {len(lyrics_text)}字符")

        # 记录原始参数值
        self.logger.info(f"原始参数值:")
        self.logger.info(f"  押韵方案复杂度: {rhyme_scheme:.4f}")
        self.logger.info(f"  隐喻密度: {metaphor_density:.4f}")
        self.logger.info(f"  词汇丰富度: {vocabulary_richness:.4f}")
        self.logger.info(f"  国际传播潜力: {international_appeal:.4f}")

        # 1. 押韵方案评分（25分）- 对应大模型15分
        self.logger.info(f"--- 押韵方案评分计算 ---")
        self.logger.info(f"输入值: {rhyme_scheme:.4f}")

        # 使用连续评分函数而不是阶梯函数
        if rhyme_scheme >= 0.7:
            rhyme_score = 25  # 优秀
            grade = "优秀"
        elif rhyme_scheme >= 0.5:
            # 线性插值：0.5-0.7 对应 20-25分
            rhyme_score = 20 + (rhyme_scheme - 0.5) / 0.2 * 5
            grade = "良好+"
        elif rhyme_scheme >= 0.3:
            # 线性插值：0.3-0.5 对应 15-20分
            rhyme_score = 15 + (rhyme_scheme - 0.3) / 0.2 * 5
            grade = "良好"
        elif rhyme_scheme >= 0.1:
            # 线性插值：0.1-0.3 对应 8-15分
            rhyme_score = 8 + (rhyme_scheme - 0.1) / 0.2 * 7
            grade = "一般"
        else:
            # 线性插值：0-0.1 对应 2-8分
            rhyme_score = 2 + rhyme_scheme / 0.1 * 6
            grade = "较差"

        details["rhyme_scheme"] = f"押韵方案复杂度{rhyme_scheme:.4f}，评级{grade}，得分{rhyme_score:.1f}/25"
        self.logger.info(f"押韵方案评分: {rhyme_scheme:.4f} -> {grade} -> {rhyme_score:.1f}/25分")
        self.logger.info(f"评分逻辑: 使用连续函数，避免阶梯效应")

        # 2. 隐喻密度评分（25分）- 对应大模型18分
        self.logger.info(f"--- 隐喻密度评分计算 ---")
        self.logger.info(f"输入值: {metaphor_density:.4f}")

        # 转换为每行隐喻数的描述
        if metaphor_density > 0:
            lines_per_metaphor = 1 / metaphor_density
            self.logger.info(f"隐喻频率: 每{lines_per_metaphor:.1f}行1个隐喻")
        else:
            lines_per_metaphor = float('inf')
            self.logger.info(f"隐喻频率: 无隐喻")

        # 使用连续评分函数
        if metaphor_density >= 0.25:  # 每4行1个隐喻以上
            metaphor_score = 25
            grade = "优秀"
        elif metaphor_density >= 0.18:  # 每5.6行1个隐喻
            # 线性插值：0.18-0.25 对应 22-25分
            metaphor_score = 22 + (metaphor_density - 0.18) / 0.07 * 3
            grade = "良好+"
        elif metaphor_density >= 0.1:  # 每10行1个隐喻
            # 线性插值：0.1-0.18 对应 15-22分
            metaphor_score = 15 + (metaphor_density - 0.1) / 0.08 * 7
            grade = "良好"
        elif metaphor_density >= 0.05:  # 每20行1个隐喻
            # 线性插值：0.05-0.1 对应 8-15分
            metaphor_score = 8 + (metaphor_density - 0.05) / 0.05 * 7
            grade = "一般"
        else:
            # 线性插值：0-0.05 对应 2-8分
            metaphor_score = 2 + metaphor_density / 0.05 * 6
            grade = "较差"

        if metaphor_density > 0:
            details["metaphor_density"] = f"隐喻密度{metaphor_density:.4f}(每{lines_per_metaphor:.1f}行1个)，评级{grade}，得分{metaphor_score:.1f}/25"
        else:
            details["metaphor_density"] = f"隐喻密度{metaphor_density:.4f}(无隐喻)，评级{grade}，得分{metaphor_score:.1f}/25"

        self.logger.info(f"隐喻密度评分: {metaphor_density:.4f} -> {grade} -> {metaphor_score:.1f}/25分")
        self.logger.info(f"评分逻辑: 连续函数，考虑隐喻使用的适度性")

        # 3. 词汇丰富度评分（30分）- 对应大模型28分
        self.logger.info(f"--- 词汇丰富度评分计算 ---")
        self.logger.info(f"输入值: {vocabulary_richness:.4f}")

        # 使用连续评分函数
        if vocabulary_richness >= 0.8:  # 极高丰富度
            vocab_score = 30
            grade = "优秀"
        elif vocabulary_richness >= 0.6:  # 高丰富度
            # 线性插值：0.6-0.8 对应 26-30分
            vocab_score = 26 + (vocabulary_richness - 0.6) / 0.2 * 4
            grade = "良好+"
        elif vocabulary_richness >= 0.4:  # 中等丰富度
            # 线性插值：0.4-0.6 对应 18-26分
            vocab_score = 18 + (vocabulary_richness - 0.4) / 0.2 * 8
            grade = "良好"
        elif vocabulary_richness >= 0.2:  # 较低丰富度
            # 线性插值：0.2-0.4 对应 10-18分
            vocab_score = 10 + (vocabulary_richness - 0.2) / 0.2 * 8
            grade = "一般"
        else:  # 很低丰富度
            # 线性插值：0-0.2 对应 3-10分
            vocab_score = 3 + vocabulary_richness / 0.2 * 7
            grade = "较差"

        details["vocabulary_richness"] = f"词汇丰富度{vocabulary_richness:.4f}，评级{grade}，得分{vocab_score:.1f}/30"
        self.logger.info(f"词汇丰富度评分: {vocabulary_richness:.4f} -> {grade} -> {vocab_score:.1f}/30分")
        self.logger.info(f"评分逻辑: TTR值越高表示词汇越丰富")

        # 4. 国际传播潜力评分（20分）- 对应大模型10分
        self.logger.info(f"--- 国际传播潜力评分计算 ---")
        self.logger.info(f"输入值: {international_appeal:.4f}")

        # 转换为百分比显示
        appeal_percentage = international_appeal * 100
        self.logger.info(f"英文词汇比例: {appeal_percentage:.1f}%")

        # 使用连续评分函数
        if international_appeal >= 0.5:  # 50%以上英文
            appeal_score = 20
            grade = "优秀"
        elif international_appeal >= 0.3:  # 30-50%英文
            # 线性插值：0.3-0.5 对应 16-20分
            appeal_score = 16 + (international_appeal - 0.3) / 0.2 * 4
            grade = "良好+"
        elif international_appeal >= 0.2:  # 20-30%英文
            # 线性插值：0.2-0.3 对应 12-16分
            appeal_score = 12 + (international_appeal - 0.2) / 0.1 * 4
            grade = "良好"
        elif international_appeal >= 0.1:  # 10-20%英文
            # 线性插值：0.1-0.2 对应 6-12分
            appeal_score = 6 + (international_appeal - 0.1) / 0.1 * 6
            grade = "一般"
        else:  # 10%以下英文
            # 线性插值：0-0.1 对应 2-6分
            appeal_score = 2 + international_appeal / 0.1 * 4
            grade = "较差"

        details["international_appeal"] = f"国际传播潜力{appeal_percentage:.1f}%，评级{grade}，得分{appeal_score:.1f}/20"
        self.logger.info(f"国际传播潜力评分: {international_appeal:.4f}({appeal_percentage:.1f}%) -> {grade} -> {appeal_score:.1f}/20分")
        self.logger.info(f"评分逻辑: 英文词汇比例越高，国际传播潜力越大")

        # 总分计算
        score = rhyme_score + metaphor_score + vocab_score + appeal_score

        self.logger.info(f"=== 歌词分析总分计算 ===")
        self.logger.info(f"押韵方案得分: {rhyme_score:.1f}/25")
        self.logger.info(f"隐喻密度得分: {metaphor_score:.1f}/25")
        self.logger.info(f"词汇丰富度得分: {vocab_score:.1f}/30")
        self.logger.info(f"国际传播潜力得分: {appeal_score:.1f}/20")
        self.logger.info(f"总分: {score:.1f}/100")
        self.logger.info(f"=== 歌词分析评分完成 ===")

        # 验证分数合理性
        if score < 0 or score > 100:
            self.logger.warning(f"⚠️ 异常分数: {score:.1f}，可能存在计算错误")
        elif score == 74.83:
            self.logger.warning(f"⚠️ 固定分数: {score:.1f}，可能未使用实际参数")
        else:
            self.logger.info(f"✅ 分数正常: {score:.1f}，基于实际歌词分析")

        return {
            "score": score,
            "details": details
        }

    def calculate_rhyme_scheme(self, lines):
        """
        计算押韵方案复杂度

        Args:
            lines: 歌词行列表

        Returns:
            float: 押韵复杂度 (0-1)
        """
        if len(lines) < 4:
            return 0.0

        # 提取每行的最后一个词作为韵脚
        rhyme_words = []
        for line in lines:
            words = line.strip().split()
            if words:
                # 取最后一个词的后缀作为韵脚
                last_word = words[-1].lower().strip('.,!?;:"()[]')
                if len(last_word) >= 2:
                    rhyme_words.append(last_word[-2:])  # 取后两个字符

        if len(rhyme_words) < 4:
            return 0.0

        # 计算押韵模式
        rhyme_pairs = 0
        for i in range(len(rhyme_words) - 1):
            for j in range(i + 1, min(i + 4, len(rhyme_words))):  # 检查附近的行
                if rhyme_words[i] == rhyme_words[j]:
                    rhyme_pairs += 1

        # 押韵复杂度：押韵对数 / 可能的最大押韵对数
        max_pairs = min(len(rhyme_words) // 2, 10)
        rhyme_complexity = min(rhyme_pairs / max_pairs, 1.0) if max_pairs > 0 else 0.0

        return rhyme_complexity

    def calculate_metaphor_density(self, lyrics):
        """
        计算隐喻密度

        Args:
            lyrics: 歌词文本

        Returns:
            float: 隐喻密度 (每行隐喻数量)
        """
        # 常见的隐喻标志词
        metaphor_indicators = [
            '像', '如', '似', '仿佛', '好像', '犹如', '宛如', '如同',
            'like', 'as', 'seems', 'appears', 'resembles'
        ]

        lines = lyrics.split('\n')
        metaphor_count = 0

        for line in lines:
            line_lower = line.lower()
            for indicator in metaphor_indicators:
                metaphor_count += line_lower.count(indicator)

        # 计算每行平均隐喻数量
        metaphor_density = metaphor_count / len(lines) if len(lines) > 0 else 0.0

        return metaphor_density
