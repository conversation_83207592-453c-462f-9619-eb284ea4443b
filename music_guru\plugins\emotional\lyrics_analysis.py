#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
歌词分析插件

MIT License
Copyright (c) 2025 Music Guru
"""

import logging
from core.plugin_manager import Plugin
from utils.lyrics_analyzer import LyricsAnalyzer


class LyricsAnalysisPlugin(Plugin):
    """歌词分析插件"""

    def __init__(self):
        super().__init__()
        self.name = "歌词分析"
        self.description = "评价歌词质量（叙事结构/语言表达）"
        self.dimension = "emotional"
        self.category = "lyrics_analysis"

        # 参数设置
        self.parameters = {
            "lyrics_text": "",  # 歌词文本
            "rhyme_scheme": 0,  # 押韵方案复杂度
            "metaphor_density": 0,  # 隐喻密度
            "vocabulary_richness": 0,  # 词汇丰富度
            "international_appeal": 0  # 国际传播潜力
        }

        # 创建歌词分析器
        self.analyzer = LyricsAnalyzer()

        # 创建日志记录器
        self.logger = logging.getLogger("LyricsAnalysisPlugin")

    def evaluate(self, data):
        """
        评价歌词质量

        评分标准：
        - 押韵方案复杂度＞0.7（+3分）
        - 隐喻密度＞每8行1个（+2分）
        - 词汇丰富度＞0.6（+2分）
        - 国际传播潜力＞0.5（+3分）

        满分：10分
        """
        self.logger.info(f"开始评价歌词质量: {data.title}")

        score = 0
        details = {}

        # 获取参数 - 优先从实际歌词数据获取
        lyrics_text = getattr(data, 'lyrics', '') or self.parameters.get("lyrics_text", "")
        rhyme_scheme = self.parameters.get("rhyme_scheme", 0)
        metaphor_density = self.parameters.get("metaphor_density", 0)
        vocabulary_richness = self.parameters.get("vocabulary_richness", 0)
        international_appeal = self.parameters.get("international_appeal", 0)

        # 如果有实际歌词，重新计算参数
        if lyrics_text and len(lyrics_text) > 50:  # 确保有足够的歌词内容
            self.logger.info("检测到实际歌词，重新计算参数")

            # 计算歌词的基本统计信息
            lines = lyrics_text.split('\n')
            words = lyrics_text.split()
            unique_words = set(word.lower().strip('.,!?;:"()[]') for word in words if word.strip())

            # 重新计算词汇丰富度 (Type-Token Ratio)
            if len(words) > 0:
                vocabulary_richness = len(unique_words) / len(words)
                self.logger.info(f"重新计算词汇丰富度: {vocabulary_richness:.3f}")

            # 重新计算押韵方案复杂度
            rhyme_scheme = self.calculate_rhyme_scheme(lines)
            self.logger.info(f"重新计算押韵复杂度: {rhyme_scheme:.3f}")

            # 重新计算隐喻密度
            metaphor_density = self.calculate_metaphor_density(lyrics_text)
            self.logger.info(f"重新计算隐喻密度: {metaphor_density:.3f}")

            # 重新计算国际传播潜力（基于英文词汇比例）
            english_words = sum(1 for word in words if word.isascii() and word.isalpha())
            international_appeal = english_words / len(words) if len(words) > 0 else 0
            self.logger.info(f"重新计算国际传播潜力: {international_appeal:.3f}")

        self.logger.info(f"歌词文本长度: {len(lyrics_text) if lyrics_text else 0}")
        self.logger.debug(f"参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                         f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")

        # 加载歌词
        if lyrics_text:
            self.logger.info("加载歌词文本")
            loaded = self.analyzer.load_lyrics(lyrics_text)
            self.logger.info(f"歌词加载{'成功' if loaded else '失败'}")

            if loaded:
                self.logger.info(f"歌词行数: {len(self.analyzer.lines)}")
                self.logger.info(f"歌词词数: {self.analyzer.word_count}")

                # 尝试分析歌词特征
                try:
                    # 更新参数
                    trending_words_ratio = self.analyzer.analyze_trending_words_ratio()
                    self.logger.info(f"年度热词覆盖率: {trending_words_ratio:.4f}")

                    synesthesia_count = self.analyzer.analyze_synesthesia_count()
                    self.logger.info(f"通感修辞数量: {synesthesia_count}")

                    scene_density = self.analyzer.analyze_scene_density()
                    self.logger.info(f"具象场景描写密度: {scene_density:.4f}")

                    lyrics_clarity = self.analyzer.analyze_lyrics_clarity()
                    self.logger.info(f"歌词语义模糊度: {lyrics_clarity:.4f}")

                    plot_structure = self.analyzer.analyze_plot_structure()
                    self.logger.info(f"情感转折点符合三幕剧结构: {plot_structure}")

                    # 更新参数
                    rhyme_scheme = max(rhyme_scheme, trending_words_ratio)
                    metaphor_density = max(metaphor_density, synesthesia_count / max(len(self.analyzer.lines), 1))
                    vocabulary_richness = max(vocabulary_richness, 1 - lyrics_clarity)
                    international_appeal = max(international_appeal, scene_density / 10)

                    self.logger.info(f"更新后的参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                                    f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")
                except Exception as e:
                    self.logger.error(f"分析歌词特征失败: {e}", exc_info=True)
        else:
            self.logger.warning("没有歌词文本")

            # 如果没有歌词，尝试从音频特征推断一些参数
            # 这是一个启发式方法，可以根据实际情况调整
            try:
                # 检查音乐数据中是否有情感相关的数据
                if hasattr(data, 'emotional_data'):
                    # 从情感数据中提取一些参数
                    emotion_delivery = data.emotional_data.get("emotion_delivery", {})
                    narrative_tension = data.emotional_data.get("narrative_tension", {})
                    era_resonance = data.emotional_data.get("era_resonance", {})

                    # 从音频特征中提取一些参数
                    chorus_range = emotion_delivery.get("chorus_range", 0)
                    synesthesia_count = emotion_delivery.get("synesthesia_count", 0)
                    scene_density = narrative_tension.get("scene_density", 0)
                    lyrics_clarity = narrative_tension.get("lyrics_clarity", 0)
                    trending_words_ratio = era_resonance.get("trending_words_ratio", 0)

                    # 更新参数
                    if chorus_range > 0:
                        rhyme_scheme = max(rhyme_scheme, chorus_range / 10)
                    if synesthesia_count > 0:
                        metaphor_density = max(metaphor_density, synesthesia_count / 10)
                    if scene_density > 0:
                        international_appeal = max(international_appeal, scene_density / 10)
                    if lyrics_clarity > 0:
                        vocabulary_richness = max(vocabulary_richness, 1 - lyrics_clarity)
                    if trending_words_ratio > 0:
                        rhyme_scheme = max(rhyme_scheme, trending_words_ratio)

                    self.logger.info(f"从音频特征推断的参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                                    f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")
            except Exception as e:
                self.logger.error(f"从音频特征推断参数失败: {e}", exc_info=True)

            # 如果参数仍然为0，设置一些默认值
            if rhyme_scheme == 0:
                rhyme_scheme = 0.3  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认押韵方案复杂度: {rhyme_scheme}")

            if metaphor_density == 0:
                metaphor_density = 0.05  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认隐喻密度: {metaphor_density}")

            if vocabulary_richness == 0:
                vocabulary_richness = 0.4  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认词汇丰富度: {vocabulary_richness}")

            if international_appeal == 0:
                international_appeal = 0.2  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认国际传播潜力: {international_appeal}")

            self.logger.info(f"最终参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                            f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")

        # 评分计算（参考大模型评分标准）
        self.logger.info("开始评分计算")

        # 1. 押韵方案评分（25分）- 对应大模型15分
        if rhyme_scheme >= 0.5:  # 降低阈值，对应大模型2级复杂度
            rhyme_score = 25  # 优秀（对应大模型+15分）
            details["rhyme_scheme"] = f"押韵方案复杂度为{rhyme_scheme:.2f}（2级：AA-BB-CC），+25分"
            self.logger.info(f"押韵方案复杂度为{rhyme_scheme:.2f}，符合要求，+25分")
        elif rhyme_scheme >= 0.3:
            rhyme_score = 15  # 良好
            details["rhyme_scheme"] = f"押韵方案复杂度为{rhyme_scheme:.2f}，有一定复杂度，+15分"
            self.logger.info(f"押韵方案复杂度为{rhyme_scheme:.2f}，有一定复杂度，+15分")
        else:
            rhyme_score = 5  # 一般
            details["rhyme_scheme"] = f"押韵方案复杂度为{rhyme_scheme:.2f}，较为简单，+5分"
            self.logger.info(f"押韵方案复杂度为{rhyme_scheme:.2f}，较为简单，+5分")

        # 2. 隐喻密度评分（25分）- 对应大模型18分
        if metaphor_density >= 0.18:  # 对应大模型每5.3行1个
            metaphor_score = 25  # 优秀（对应大模型+18分）
            details["metaphor_density"] = f"隐喻密度为每{1/metaphor_density:.1f}行1个，+25分"
            self.logger.info(f"隐喻密度为每{1/metaphor_density:.1f}行1个，符合要求，+25分")
        elif metaphor_density >= 0.1:
            metaphor_score = 15  # 良好
            details["metaphor_density"] = f"隐喻密度为每{1/metaphor_density:.1f}行1个，适度使用，+15分"
            self.logger.info(f"隐喻密度为每{1/metaphor_density:.1f}行1个，适度使用，+15分")
        else:
            metaphor_score = 5  # 一般
            details["metaphor_density"] = f"隐喻密度为每{1/metaphor_density if metaphor_density else 0:.1f}行1个，使用较少，+5分"
            self.logger.info(f"隐喻密度为每{1/metaphor_density if metaphor_density else 0:.1f}行1个，使用较少，+5分")

        # 3. 词汇丰富度评分（30分）- 对应大模型28分
        if vocabulary_richness >= 0.6:
            vocab_score = 30  # 优秀（对应大模型+28分）
            details["vocabulary_richness"] = f"词汇丰富度为{vocabulary_richness:.2f}，大于0.6，+30分"
            self.logger.info(f"词汇丰富度为{vocabulary_richness:.2f}，大于0.6，+30分")
        elif vocabulary_richness >= 0.4:
            vocab_score = 20  # 良好
            details["vocabulary_richness"] = f"词汇丰富度为{vocabulary_richness:.2f}，适中，+20分"
            self.logger.info(f"词汇丰富度为{vocabulary_richness:.2f}，适中，+20分")
        else:
            vocab_score = 10  # 一般
            details["vocabulary_richness"] = f"词汇丰富度为{vocabulary_richness:.2f}，较低，+10分"
            self.logger.info(f"词汇丰富度为{vocabulary_richness:.2f}，较低，+10分")

        # 4. 国际传播潜力评分（20分）- 对应大模型10分
        if international_appeal >= 0.3:  # 对应大模型32/100
            appeal_score = 20  # 优秀（对应大模型+10分）
            details["international_appeal"] = f"国际传播潜力为{international_appeal*100:.0f}/100（中文直白叙事限制跨文化传播），+20分"
            self.logger.info(f"国际传播潜力为{international_appeal:.2f}，符合要求，+20分")
        elif international_appeal >= 0.2:
            appeal_score = 12  # 良好
            details["international_appeal"] = f"国际传播潜力为{international_appeal*100:.0f}/100，有一定潜力，+12分"
            self.logger.info(f"国际传播潜力为{international_appeal:.2f}，有一定潜力，+12分")
        else:
            appeal_score = 5  # 一般
            details["international_appeal"] = f"国际传播潜力为{international_appeal*100:.0f}/100，较为有限，+5分"
            self.logger.info(f"国际传播潜力为{international_appeal:.2f}，较为有限，+5分")

        # 总分计算
        score = rhyme_score + metaphor_score + vocab_score + appeal_score

        self.logger.info(f"评分计算完成，原始分数: {score}/100，标准化分数: {score}/100")

        return {
            "score": score,
            "details": details
        }

    def calculate_rhyme_scheme(self, lines):
        """
        计算押韵方案复杂度

        Args:
            lines: 歌词行列表

        Returns:
            float: 押韵复杂度 (0-1)
        """
        if len(lines) < 4:
            return 0.0

        # 提取每行的最后一个词作为韵脚
        rhyme_words = []
        for line in lines:
            words = line.strip().split()
            if words:
                # 取最后一个词的后缀作为韵脚
                last_word = words[-1].lower().strip('.,!?;:"()[]')
                if len(last_word) >= 2:
                    rhyme_words.append(last_word[-2:])  # 取后两个字符

        if len(rhyme_words) < 4:
            return 0.0

        # 计算押韵模式
        rhyme_pairs = 0
        for i in range(len(rhyme_words) - 1):
            for j in range(i + 1, min(i + 4, len(rhyme_words))):  # 检查附近的行
                if rhyme_words[i] == rhyme_words[j]:
                    rhyme_pairs += 1

        # 押韵复杂度：押韵对数 / 可能的最大押韵对数
        max_pairs = min(len(rhyme_words) // 2, 10)
        rhyme_complexity = min(rhyme_pairs / max_pairs, 1.0) if max_pairs > 0 else 0.0

        return rhyme_complexity

    def calculate_metaphor_density(self, lyrics):
        """
        计算隐喻密度

        Args:
            lyrics: 歌词文本

        Returns:
            float: 隐喻密度 (每行隐喻数量)
        """
        # 常见的隐喻标志词
        metaphor_indicators = [
            '像', '如', '似', '仿佛', '好像', '犹如', '宛如', '如同',
            'like', 'as', 'seems', 'appears', 'resembles'
        ]

        lines = lyrics.split('\n')
        metaphor_count = 0

        for line in lines:
            line_lower = line.lower()
            for indicator in metaphor_indicators:
                metaphor_count += line_lower.count(indicator)

        # 计算每行平均隐喻数量
        metaphor_density = metaphor_count / len(lines) if len(lines) > 0 else 0.0

        return metaphor_density
