#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
歌词分析插件

MIT License
Copyright (c) 2025 Music Guru
"""

import logging
from core.plugin_manager import Plugin
from utils.lyrics_analyzer import LyricsAnalyzer


class LyricsAnalysisPlugin(Plugin):
    """歌词分析插件"""

    def __init__(self):
        super().__init__()
        self.name = "歌词分析"
        self.description = "评价歌词质量（叙事结构/语言表达）"
        self.dimension = "emotional"
        self.category = "lyrics_analysis"

        # 参数设置
        self.parameters = {
            "lyrics_text": "",  # 歌词文本
            "rhyme_scheme": 0,  # 押韵方案复杂度
            "metaphor_density": 0,  # 隐喻密度
            "vocabulary_richness": 0,  # 词汇丰富度
            "international_appeal": 0  # 国际传播潜力
        }

        # 创建歌词分析器
        self.analyzer = LyricsAnalyzer()

        # 创建日志记录器
        self.logger = logging.getLogger("LyricsAnalysisPlugin")

    def evaluate(self, data):
        """
        评价歌词质量

        评分标准：
        - 押韵方案复杂度＞0.7（+3分）
        - 隐喻密度＞每8行1个（+2分）
        - 词汇丰富度＞0.6（+2分）
        - 国际传播潜力＞0.5（+3分）

        满分：10分
        """
        self.logger.info(f"开始评价歌词质量: {data.title}")

        score = 0
        details = {}

        # 获取参数
        lyrics_text = self.parameters.get("lyrics_text", "")
        rhyme_scheme = self.parameters.get("rhyme_scheme", 0)
        metaphor_density = self.parameters.get("metaphor_density", 0)
        vocabulary_richness = self.parameters.get("vocabulary_richness", 0)
        international_appeal = self.parameters.get("international_appeal", 0)

        self.logger.info(f"歌词文本长度: {len(lyrics_text) if lyrics_text else 0}")
        self.logger.debug(f"参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                         f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")

        # 加载歌词
        if lyrics_text:
            self.logger.info("加载歌词文本")
            loaded = self.analyzer.load_lyrics(lyrics_text)
            self.logger.info(f"歌词加载{'成功' if loaded else '失败'}")

            if loaded:
                self.logger.info(f"歌词行数: {len(self.analyzer.lines)}")
                self.logger.info(f"歌词词数: {self.analyzer.word_count}")

                # 尝试分析歌词特征
                try:
                    # 更新参数
                    trending_words_ratio = self.analyzer.analyze_trending_words_ratio()
                    self.logger.info(f"年度热词覆盖率: {trending_words_ratio:.4f}")

                    synesthesia_count = self.analyzer.analyze_synesthesia_count()
                    self.logger.info(f"通感修辞数量: {synesthesia_count}")

                    scene_density = self.analyzer.analyze_scene_density()
                    self.logger.info(f"具象场景描写密度: {scene_density:.4f}")

                    lyrics_clarity = self.analyzer.analyze_lyrics_clarity()
                    self.logger.info(f"歌词语义模糊度: {lyrics_clarity:.4f}")

                    plot_structure = self.analyzer.analyze_plot_structure()
                    self.logger.info(f"情感转折点符合三幕剧结构: {plot_structure}")

                    # 更新参数
                    rhyme_scheme = max(rhyme_scheme, trending_words_ratio)
                    metaphor_density = max(metaphor_density, synesthesia_count / max(len(self.analyzer.lines), 1))
                    vocabulary_richness = max(vocabulary_richness, 1 - lyrics_clarity)
                    international_appeal = max(international_appeal, scene_density / 10)

                    self.logger.info(f"更新后的参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                                    f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")
                except Exception as e:
                    self.logger.error(f"分析歌词特征失败: {e}", exc_info=True)
        else:
            self.logger.warning("没有歌词文本")

            # 如果没有歌词，尝试从音频特征推断一些参数
            # 这是一个启发式方法，可以根据实际情况调整
            try:
                # 检查音乐数据中是否有情感相关的数据
                if hasattr(data, 'emotional_data'):
                    # 从情感数据中提取一些参数
                    emotion_delivery = data.emotional_data.get("emotion_delivery", {})
                    narrative_tension = data.emotional_data.get("narrative_tension", {})
                    era_resonance = data.emotional_data.get("era_resonance", {})

                    # 从音频特征中提取一些参数
                    chorus_range = emotion_delivery.get("chorus_range", 0)
                    synesthesia_count = emotion_delivery.get("synesthesia_count", 0)
                    scene_density = narrative_tension.get("scene_density", 0)
                    lyrics_clarity = narrative_tension.get("lyrics_clarity", 0)
                    trending_words_ratio = era_resonance.get("trending_words_ratio", 0)

                    # 更新参数
                    if chorus_range > 0:
                        rhyme_scheme = max(rhyme_scheme, chorus_range / 10)
                    if synesthesia_count > 0:
                        metaphor_density = max(metaphor_density, synesthesia_count / 10)
                    if scene_density > 0:
                        international_appeal = max(international_appeal, scene_density / 10)
                    if lyrics_clarity > 0:
                        vocabulary_richness = max(vocabulary_richness, 1 - lyrics_clarity)
                    if trending_words_ratio > 0:
                        rhyme_scheme = max(rhyme_scheme, trending_words_ratio)

                    self.logger.info(f"从音频特征推断的参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                                    f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")
            except Exception as e:
                self.logger.error(f"从音频特征推断参数失败: {e}", exc_info=True)

            # 如果参数仍然为0，设置一些默认值
            if rhyme_scheme == 0:
                rhyme_scheme = 0.3  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认押韵方案复杂度: {rhyme_scheme}")

            if metaphor_density == 0:
                metaphor_density = 0.05  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认隐喻密度: {metaphor_density}")

            if vocabulary_richness == 0:
                vocabulary_richness = 0.4  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认词汇丰富度: {vocabulary_richness}")

            if international_appeal == 0:
                international_appeal = 0.2  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认国际传播潜力: {international_appeal}")

            self.logger.info(f"最终参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                            f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")

        # 评分计算
        self.logger.info("开始评分计算")

        # 1. 押韵方案复杂度＞0.7（+3分）
        if rhyme_scheme > 0.7:
            score += 3
            details["rhyme_scheme"] = f"押韵方案复杂度为{rhyme_scheme:.2f}，大于0.7，+3分"
            self.logger.info(f"押韵方案复杂度为{rhyme_scheme:.2f}，大于0.7，+3分")
        else:
            details["rhyme_scheme"] = f"押韵方案复杂度为{rhyme_scheme:.2f}，不符合要求，+0分"
            self.logger.info(f"押韵方案复杂度为{rhyme_scheme:.2f}，不符合要求，+0分")

        # 2. 隐喻密度＞每8行1个（+2分）
        if metaphor_density > 0.125:  # 1/8 = 0.125
            score += 2
            details["metaphor_density"] = f"隐喻密度为每{1/metaphor_density:.1f}行1个，大于每8行1个，+2分"
            self.logger.info(f"隐喻密度为每{1/metaphor_density:.1f}行1个，大于每8行1个，+2分")
        else:
            details["metaphor_density"] = f"隐喻密度为每{1/metaphor_density if metaphor_density else 0:.1f}行1个，不符合要求，+0分"
            self.logger.info(f"隐喻密度为每{1/metaphor_density if metaphor_density else 0:.1f}行1个，不符合要求，+0分")

        # 3. 词汇丰富度＞0.6（+2分）
        if vocabulary_richness > 0.6:
            score += 2
            details["vocabulary_richness"] = f"词汇丰富度为{vocabulary_richness:.2f}，大于0.6，+2分"
            self.logger.info(f"词汇丰富度为{vocabulary_richness:.2f}，大于0.6，+2分")
        else:
            details["vocabulary_richness"] = f"词汇丰富度为{vocabulary_richness:.2f}，不符合要求，+0分"
            self.logger.info(f"词汇丰富度为{vocabulary_richness:.2f}，不符合要求，+0分")

        # 4. 国际传播潜力＞0.5（+3分）
        if international_appeal > 0.5:
            score += 3
            details["international_appeal"] = f"国际传播潜力为{international_appeal:.2f}，大于0.5，+3分"
            self.logger.info(f"国际传播潜力为{international_appeal:.2f}，大于0.5，+3分")
        else:
            details["international_appeal"] = f"国际传播潜力为{international_appeal:.2f}，不符合要求，+0分"
            self.logger.info(f"国际传播潜力为{international_appeal:.2f}，不符合要求，+0分")

        # 标准化分数到100分制
        normalized_score = (score / 10) * 100

        self.logger.info(f"评分计算完成，原始分数: {score}/10，标准化分数: {normalized_score}/100")

        return {
            "score": normalized_score,
            "details": details
        }
