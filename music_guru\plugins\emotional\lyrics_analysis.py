#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
歌词分析插件

MIT License
Copyright (c) 2025 Music Guru
"""

import logging
from core.plugin_manager import Plugin
from utils.lyrics_analyzer import LyricsAnalyzer


class LyricsAnalysisPlugin(Plugin):
    """歌词分析插件"""

    def __init__(self):
        super().__init__()
        self.name = "歌词分析"
        self.description = "评价歌词质量（叙事结构/语言表达）"
        self.dimension = "emotional"
        self.category = "lyrics_analysis"

        # 参数设置
        self.parameters = {
            "lyrics_text": "",  # 歌词文本
            "rhyme_scheme": 0,  # 押韵方案复杂度
            "metaphor_density": 0,  # 隐喻密度
            "vocabulary_richness": 0,  # 词汇丰富度
            "international_appeal": 0  # 国际传播潜力
        }

        # 创建歌词分析器
        self.analyzer = LyricsAnalyzer()

        # 创建日志记录器
        self.logger = logging.getLogger("LyricsAnalysisPlugin")

    def evaluate(self, data):
        """
        评价歌词质量

        评分标准：
        - 押韵方案复杂度＞0.7（+3分）
        - 隐喻密度＞每8行1个（+2分）
        - 词汇丰富度＞0.6（+2分）
        - 国际传播潜力＞0.5（+3分）

        满分：10分
        """
        self.logger.info(f"开始评价歌词质量: {data.title}")

        score = 0
        details = {}

        # 获取参数 - 只使用实际歌词，不允许固定文本
        lyrics_text = getattr(data, 'lyrics', '')

        # 如果没有歌词，尝试在线搜索
        if not lyrics_text or len(lyrics_text.strip()) < 10:
            self.logger.warning("未找到歌词文本，尝试在线搜索")
            lyrics_text = self.search_lyrics_online(data)

        if not lyrics_text or len(lyrics_text.strip()) < 10:
            self.logger.error("无法获取歌词，跳过歌词分析")
            return {
                "score": 0,
                "details": {"error": "无法获取歌词文本，无法进行歌词分析"}
            }

        self.logger.info(f"获取到歌词文本，长度: {len(lyrics_text)}字符")

        # 基于实际歌词重新计算所有参数
        self.logger.info("基于实际歌词重新计算所有参数")

        # 计算歌词的基本统计信息
        lines = [line.strip() for line in lyrics_text.split('\n') if line.strip()]
        words = lyrics_text.split()
        unique_words = set(word.lower().strip('.,!?;:"()[]') for word in words if word.strip())

        self.logger.info(f"歌词统计: {len(lines)}行, {len(words)}词, {len(unique_words)}独特词")

        # 1. 计算词汇丰富度 (Type-Token Ratio)
        vocabulary_richness = len(unique_words) / len(words) if len(words) > 0 else 0
        self.logger.info(f"词汇丰富度: {vocabulary_richness:.4f}")

        # 2. 计算押韵方案复杂度
        rhyme_scheme = self.calculate_rhyme_scheme_advanced(lines)
        self.logger.info(f"押韵复杂度: {rhyme_scheme:.4f}")

        # 3. 计算隐喻密度
        metaphor_density = self.calculate_metaphor_density_advanced(lyrics_text)
        self.logger.info(f"隐喻密度: {metaphor_density:.4f}")

        # 4. 计算国际传播潜力
        international_appeal = self.calculate_international_appeal_advanced(lyrics_text, words)
        self.logger.info(f"国际传播潜力: {international_appeal:.4f}")

        self.logger.info(f"歌词文本长度: {len(lyrics_text) if lyrics_text else 0}")
        self.logger.debug(f"参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                         f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")

        # 加载歌词
        if lyrics_text:
            self.logger.info("加载歌词文本")
            loaded = self.analyzer.load_lyrics(lyrics_text)
            self.logger.info(f"歌词加载{'成功' if loaded else '失败'}")

            if loaded:
                self.logger.info(f"歌词行数: {len(self.analyzer.lines)}")
                self.logger.info(f"歌词词数: {self.analyzer.word_count}")

                # 尝试分析歌词特征
                try:
                    # 更新参数
                    trending_words_ratio = self.analyzer.analyze_trending_words_ratio()
                    self.logger.info(f"年度热词覆盖率: {trending_words_ratio:.4f}")

                    synesthesia_count = self.analyzer.analyze_synesthesia_count()
                    self.logger.info(f"通感修辞数量: {synesthesia_count}")

                    scene_density = self.analyzer.analyze_scene_density()
                    self.logger.info(f"具象场景描写密度: {scene_density:.4f}")

                    lyrics_clarity = self.analyzer.analyze_lyrics_clarity()
                    self.logger.info(f"歌词语义模糊度: {lyrics_clarity:.4f}")

                    plot_structure = self.analyzer.analyze_plot_structure()
                    self.logger.info(f"情感转折点符合三幕剧结构: {plot_structure}")

                    # 更新参数
                    rhyme_scheme = max(rhyme_scheme, trending_words_ratio)
                    metaphor_density = max(metaphor_density, synesthesia_count / max(len(self.analyzer.lines), 1))
                    vocabulary_richness = max(vocabulary_richness, 1 - lyrics_clarity)
                    international_appeal = max(international_appeal, scene_density / 10)

                    self.logger.info(f"更新后的参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                                    f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")
                except Exception as e:
                    self.logger.error(f"分析歌词特征失败: {e}", exc_info=True)
        else:
            self.logger.warning("没有歌词文本")

            # 如果没有歌词，尝试从音频特征推断一些参数
            # 这是一个启发式方法，可以根据实际情况调整
            try:
                # 检查音乐数据中是否有情感相关的数据
                if hasattr(data, 'emotional_data'):
                    # 从情感数据中提取一些参数
                    emotion_delivery = data.emotional_data.get("emotion_delivery", {})
                    narrative_tension = data.emotional_data.get("narrative_tension", {})
                    era_resonance = data.emotional_data.get("era_resonance", {})

                    # 从音频特征中提取一些参数
                    chorus_range = emotion_delivery.get("chorus_range", 0)
                    synesthesia_count = emotion_delivery.get("synesthesia_count", 0)
                    scene_density = narrative_tension.get("scene_density", 0)
                    lyrics_clarity = narrative_tension.get("lyrics_clarity", 0)
                    trending_words_ratio = era_resonance.get("trending_words_ratio", 0)

                    # 更新参数
                    if chorus_range > 0:
                        rhyme_scheme = max(rhyme_scheme, chorus_range / 10)
                    if synesthesia_count > 0:
                        metaphor_density = max(metaphor_density, synesthesia_count / 10)
                    if scene_density > 0:
                        international_appeal = max(international_appeal, scene_density / 10)
                    if lyrics_clarity > 0:
                        vocabulary_richness = max(vocabulary_richness, 1 - lyrics_clarity)
                    if trending_words_ratio > 0:
                        rhyme_scheme = max(rhyme_scheme, trending_words_ratio)

                    self.logger.info(f"从音频特征推断的参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                                    f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")
            except Exception as e:
                self.logger.error(f"从音频特征推断参数失败: {e}", exc_info=True)

            # 如果参数仍然为0，设置一些默认值
            if rhyme_scheme == 0:
                rhyme_scheme = 0.3  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认押韵方案复杂度: {rhyme_scheme}")

            if metaphor_density == 0:
                metaphor_density = 0.05  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认隐喻密度: {metaphor_density}")

            if vocabulary_richness == 0:
                vocabulary_richness = 0.4  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认词汇丰富度: {vocabulary_richness}")

            if international_appeal == 0:
                international_appeal = 0.2  # 设置一个较低但非零的默认值
                self.logger.info(f"使用默认国际传播潜力: {international_appeal}")

            self.logger.info(f"最终参数: rhyme_scheme={rhyme_scheme}, metaphor_density={metaphor_density}, "
                            f"vocabulary_richness={vocabulary_richness}, international_appeal={international_appeal}")

        # 评分计算（参考大模型评分标准）
        self.logger.info("开始评分计算")
        self.logger.info(f"=== 歌词分析详细评分过程 ===")
        self.logger.info(f"歌词文件: {data.title}")
        self.logger.info(f"歌词长度: {len(lyrics_text)}字符")

        # 记录原始参数值
        self.logger.info(f"原始参数值:")
        self.logger.info(f"  押韵方案复杂度: {rhyme_scheme:.4f}")
        self.logger.info(f"  隐喻密度: {metaphor_density:.4f}")
        self.logger.info(f"  词汇丰富度: {vocabulary_richness:.4f}")
        self.logger.info(f"  国际传播潜力: {international_appeal:.4f}")

        # 1. 押韵方案评分（25分）- 对应大模型15分
        self.logger.info(f"--- 押韵方案评分计算 ---")
        self.logger.info(f"输入值: {rhyme_scheme:.4f}")

        # 使用连续评分函数而不是阶梯函数
        if rhyme_scheme >= 0.7:
            rhyme_score = 25  # 优秀
            grade = "优秀"
        elif rhyme_scheme >= 0.5:
            # 线性插值：0.5-0.7 对应 20-25分
            rhyme_score = 20 + (rhyme_scheme - 0.5) / 0.2 * 5
            grade = "良好+"
        elif rhyme_scheme >= 0.3:
            # 线性插值：0.3-0.5 对应 15-20分
            rhyme_score = 15 + (rhyme_scheme - 0.3) / 0.2 * 5
            grade = "良好"
        elif rhyme_scheme >= 0.1:
            # 线性插值：0.1-0.3 对应 8-15分
            rhyme_score = 8 + (rhyme_scheme - 0.1) / 0.2 * 7
            grade = "一般"
        else:
            # 线性插值：0-0.1 对应 2-8分
            rhyme_score = 2 + rhyme_scheme / 0.1 * 6
            grade = "较差"

        details["rhyme_scheme"] = f"押韵方案复杂度{rhyme_scheme:.4f}，评级{grade}，得分{rhyme_score:.1f}/25"
        self.logger.info(f"押韵方案评分: {rhyme_scheme:.4f} -> {grade} -> {rhyme_score:.1f}/25分")
        self.logger.info(f"评分逻辑: 使用连续函数，避免阶梯效应")

        # 2. 隐喻密度评分（25分）- 对应大模型18分
        self.logger.info(f"--- 隐喻密度评分计算 ---")
        self.logger.info(f"输入值: {metaphor_density:.4f}")

        # 转换为每行隐喻数的描述
        if metaphor_density > 0:
            lines_per_metaphor = 1 / metaphor_density
            self.logger.info(f"隐喻频率: 每{lines_per_metaphor:.1f}行1个隐喻")
        else:
            lines_per_metaphor = float('inf')
            self.logger.info(f"隐喻频率: 无隐喻")

        # 使用连续评分函数
        if metaphor_density >= 0.25:  # 每4行1个隐喻以上
            metaphor_score = 25
            grade = "优秀"
        elif metaphor_density >= 0.18:  # 每5.6行1个隐喻
            # 线性插值：0.18-0.25 对应 22-25分
            metaphor_score = 22 + (metaphor_density - 0.18) / 0.07 * 3
            grade = "良好+"
        elif metaphor_density >= 0.1:  # 每10行1个隐喻
            # 线性插值：0.1-0.18 对应 15-22分
            metaphor_score = 15 + (metaphor_density - 0.1) / 0.08 * 7
            grade = "良好"
        elif metaphor_density >= 0.05:  # 每20行1个隐喻
            # 线性插值：0.05-0.1 对应 8-15分
            metaphor_score = 8 + (metaphor_density - 0.05) / 0.05 * 7
            grade = "一般"
        else:
            # 线性插值：0-0.05 对应 2-8分
            metaphor_score = 2 + metaphor_density / 0.05 * 6
            grade = "较差"

        if metaphor_density > 0:
            details["metaphor_density"] = f"隐喻密度{metaphor_density:.4f}(每{lines_per_metaphor:.1f}行1个)，评级{grade}，得分{metaphor_score:.1f}/25"
        else:
            details["metaphor_density"] = f"隐喻密度{metaphor_density:.4f}(无隐喻)，评级{grade}，得分{metaphor_score:.1f}/25"

        self.logger.info(f"隐喻密度评分: {metaphor_density:.4f} -> {grade} -> {metaphor_score:.1f}/25分")
        self.logger.info(f"评分逻辑: 连续函数，考虑隐喻使用的适度性")

        # 3. 词汇丰富度评分（30分）- 对应大模型28分
        self.logger.info(f"--- 词汇丰富度评分计算 ---")
        self.logger.info(f"输入值: {vocabulary_richness:.4f}")

        # 使用连续评分函数
        if vocabulary_richness >= 0.8:  # 极高丰富度
            vocab_score = 30
            grade = "优秀"
        elif vocabulary_richness >= 0.6:  # 高丰富度
            # 线性插值：0.6-0.8 对应 26-30分
            vocab_score = 26 + (vocabulary_richness - 0.6) / 0.2 * 4
            grade = "良好+"
        elif vocabulary_richness >= 0.4:  # 中等丰富度
            # 线性插值：0.4-0.6 对应 18-26分
            vocab_score = 18 + (vocabulary_richness - 0.4) / 0.2 * 8
            grade = "良好"
        elif vocabulary_richness >= 0.2:  # 较低丰富度
            # 线性插值：0.2-0.4 对应 10-18分
            vocab_score = 10 + (vocabulary_richness - 0.2) / 0.2 * 8
            grade = "一般"
        else:  # 很低丰富度
            # 线性插值：0-0.2 对应 3-10分
            vocab_score = 3 + vocabulary_richness / 0.2 * 7
            grade = "较差"

        details["vocabulary_richness"] = f"词汇丰富度{vocabulary_richness:.4f}，评级{grade}，得分{vocab_score:.1f}/30"
        self.logger.info(f"词汇丰富度评分: {vocabulary_richness:.4f} -> {grade} -> {vocab_score:.1f}/30分")
        self.logger.info(f"评分逻辑: TTR值越高表示词汇越丰富")

        # 4. 国际传播潜力评分（20分）- 对应大模型10分
        self.logger.info(f"--- 国际传播潜力评分计算 ---")
        self.logger.info(f"输入值: {international_appeal:.4f}")

        # 转换为百分比显示
        appeal_percentage = international_appeal * 100
        self.logger.info(f"英文词汇比例: {appeal_percentage:.1f}%")

        # 使用连续评分函数
        if international_appeal >= 0.5:  # 50%以上英文
            appeal_score = 20
            grade = "优秀"
        elif international_appeal >= 0.3:  # 30-50%英文
            # 线性插值：0.3-0.5 对应 16-20分
            appeal_score = 16 + (international_appeal - 0.3) / 0.2 * 4
            grade = "良好+"
        elif international_appeal >= 0.2:  # 20-30%英文
            # 线性插值：0.2-0.3 对应 12-16分
            appeal_score = 12 + (international_appeal - 0.2) / 0.1 * 4
            grade = "良好"
        elif international_appeal >= 0.1:  # 10-20%英文
            # 线性插值：0.1-0.2 对应 6-12分
            appeal_score = 6 + (international_appeal - 0.1) / 0.1 * 6
            grade = "一般"
        else:  # 10%以下英文
            # 线性插值：0-0.1 对应 2-6分
            appeal_score = 2 + international_appeal / 0.1 * 4
            grade = "较差"

        details["international_appeal"] = f"国际传播潜力{appeal_percentage:.1f}%，评级{grade}，得分{appeal_score:.1f}/20"
        self.logger.info(f"国际传播潜力评分: {international_appeal:.4f}({appeal_percentage:.1f}%) -> {grade} -> {appeal_score:.1f}/20分")
        self.logger.info(f"评分逻辑: 英文词汇比例越高，国际传播潜力越大")

        # 总分计算
        score = rhyme_score + metaphor_score + vocab_score + appeal_score

        self.logger.info(f"=== 歌词分析总分计算 ===")
        self.logger.info(f"押韵方案得分: {rhyme_score:.1f}/25")
        self.logger.info(f"隐喻密度得分: {metaphor_score:.1f}/25")
        self.logger.info(f"词汇丰富度得分: {vocab_score:.1f}/30")
        self.logger.info(f"国际传播潜力得分: {appeal_score:.1f}/20")
        self.logger.info(f"总分: {score:.1f}/100")
        self.logger.info(f"=== 歌词分析评分完成 ===")

        # 验证分数合理性
        if score < 0 or score > 100:
            self.logger.warning(f"⚠️ 异常分数: {score:.1f}，可能存在计算错误")
        elif score == 74.83:
            self.logger.warning(f"⚠️ 固定分数: {score:.1f}，可能未使用实际参数")
        else:
            self.logger.info(f"✅ 分数正常: {score:.1f}，基于实际歌词分析")

        return {
            "score": score,
            "details": details
        }

    def calculate_rhyme_scheme(self, lines):
        """
        计算押韵方案复杂度

        Args:
            lines: 歌词行列表

        Returns:
            float: 押韵复杂度 (0-1)
        """
        if len(lines) < 4:
            return 0.0

        # 提取每行的最后一个词作为韵脚
        rhyme_words = []
        for line in lines:
            words = line.strip().split()
            if words:
                # 取最后一个词的后缀作为韵脚
                last_word = words[-1].lower().strip('.,!?;:"()[]')
                if len(last_word) >= 2:
                    rhyme_words.append(last_word[-2:])  # 取后两个字符

        if len(rhyme_words) < 4:
            return 0.0

        # 计算押韵模式
        rhyme_pairs = 0
        for i in range(len(rhyme_words) - 1):
            for j in range(i + 1, min(i + 4, len(rhyme_words))):  # 检查附近的行
                if rhyme_words[i] == rhyme_words[j]:
                    rhyme_pairs += 1

        # 押韵复杂度：押韵对数 / 可能的最大押韵对数
        max_pairs = min(len(rhyme_words) // 2, 10)
        rhyme_complexity = min(rhyme_pairs / max_pairs, 1.0) if max_pairs > 0 else 0.0

        return rhyme_complexity

    def calculate_metaphor_density(self, lyrics):
        """
        计算隐喻密度

        Args:
            lyrics: 歌词文本

        Returns:
            float: 隐喻密度 (每行隐喻数量)
        """
        # 常见的隐喻标志词
        metaphor_indicators = [
            '像', '如', '似', '仿佛', '好像', '犹如', '宛如', '如同',
            'like', 'as', 'seems', 'appears', 'resembles'
        ]

        lines = lyrics.split('\n')
        metaphor_count = 0

        for line in lines:
            line_lower = line.lower()
            for indicator in metaphor_indicators:
                metaphor_count += line_lower.count(indicator)

        # 计算每行平均隐喻数量
        metaphor_density = metaphor_count / len(lines) if len(lines) > 0 else 0.0

        return metaphor_density

    def search_lyrics_online(self, data):
        """
        在线搜索歌词 - 简化版本，避免WebEngine依赖

        Args:
            data: 音乐数据对象

        Returns:
            str: 搜索到的歌词文本
        """
        try:
            # 构建搜索关键词
            title = getattr(data, 'title', '')
            artist = getattr(data, 'artist', '')

            if not title:
                self.logger.warning("无歌曲标题，无法搜索歌词")
                return ""

            # 清理标题，移除文件扩展名和特殊字符
            clean_title = title.replace('.mp3', '').replace('.wma', '').replace('.flac', '').replace('.m4a', '')
            clean_title = clean_title.split(' - ')[-1] if ' - ' in clean_title else clean_title
            clean_title = clean_title.strip()

            self.logger.info(f"尝试搜索歌词: 标题='{clean_title}', 艺术家='{artist}'")

            # 尝试从本地歌词文件搜索
            lyrics = self.search_local_lyrics(clean_title, artist)
            if lyrics:
                self.logger.info("从本地文件找到歌词")
                return lyrics

            # 尝试简单的网络搜索（不使用WebEngine）
            lyrics = self.search_lyrics_simple(clean_title, artist)
            if lyrics:
                self.logger.info("从网络搜索找到歌词")
                return lyrics

            self.logger.warning("未找到歌词，返回空")
            return ""

        except Exception as e:
            self.logger.error(f"搜索歌词失败: {e}")
            return ""

    def search_local_lyrics(self, title, artist):
        """
        搜索本地歌词文件

        Args:
            title: 歌曲标题
            artist: 艺术家

        Returns:
            str: 歌词文本
        """
        try:
            import os
            import glob

            # 可能的歌词文件扩展名
            lyrics_extensions = ['.lrc', '.txt', '.lyrics']

            # 可能的搜索路径
            search_paths = [
                os.path.dirname(getattr(self, 'current_file_path', '')),  # 音乐文件同目录
                os.path.join(os.path.dirname(__file__), '..', '..', 'lyrics'),  # lyrics目录
                os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'lyrics')  # data/lyrics目录
            ]

            # 可能的文件名模式
            search_patterns = [
                f"{title}",
                f"{artist} - {title}",
                f"{title} - {artist}",
                f"{artist}_{title}",
                f"{title}_{artist}"
            ]

            for search_path in search_paths:
                if not os.path.exists(search_path):
                    continue

                for pattern in search_patterns:
                    for ext in lyrics_extensions:
                        # 清理文件名中的特殊字符
                        clean_pattern = pattern.replace('/', '_').replace('\\', '_').replace(':', '_')
                        file_pattern = os.path.join(search_path, f"{clean_pattern}{ext}")

                        matching_files = glob.glob(file_pattern)
                        if matching_files:
                            lyrics_file = matching_files[0]
                            self.logger.info(f"找到本地歌词文件: {lyrics_file}")

                            with open(lyrics_file, 'r', encoding='utf-8') as f:
                                lyrics = f.read()

                            # 清理LRC格式的时间标签
                            if lyrics_file.endswith('.lrc'):
                                lyrics = self.clean_lrc_format(lyrics)

                            return lyrics.strip()

            return ""

        except Exception as e:
            self.logger.error(f"搜索本地歌词失败: {e}")
            return ""

    def clean_lrc_format(self, lrc_content):
        """
        清理LRC格式的时间标签

        Args:
            lrc_content: LRC格式的歌词内容

        Returns:
            str: 清理后的歌词文本
        """
        import re

        # 移除时间标签 [00:12.34]
        cleaned = re.sub(r'\[\d{2}:\d{2}\.\d{2}\]', '', lrc_content)

        # 移除空行
        lines = [line.strip() for line in cleaned.split('\n') if line.strip()]

        return '\n'.join(lines)

    def search_lyrics_simple(self, title, artist):
        """
        简单的网络歌词搜索（不使用WebEngine）

        Args:
            title: 歌曲标题
            artist: 艺术家

        Returns:
            str: 歌词文本
        """
        try:
            # 这里可以集成简单的HTTP请求搜索
            # 目前返回空以避免网络依赖
            self.logger.info("网络歌词搜索功能待实现")
            return ""

        except Exception as e:
            self.logger.error(f"网络搜索歌词失败: {e}")
            return ""

    def load_database(self, filename):
        """
        加载数据库文件

        Args:
            filename: 数据库文件名

        Returns:
            dict: 数据库内容
        """
        try:
            import json
            import os

            db_path = os.path.join(os.path.dirname(__file__), '..', '..', 'data', filename)

            if os.path.exists(db_path):
                with open(db_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                self.logger.warning(f"数据库文件不存在: {db_path}")
                return {}

        except Exception as e:
            self.logger.error(f"加载数据库失败: {e}")
            return {}

    def calculate_rhyme_scheme_advanced(self, lines):
        """
        高级押韵方案分析

        Args:
            lines: 歌词行列表

        Returns:
            float: 押韵复杂度 (0-1)
        """
        if len(lines) < 4:
            return 0.0

        # 加载押韵数据库
        rhyme_db = self.load_database('rhyme_patterns.json')
        chinese_endings = rhyme_db.get('chinese_rhyme_endings', {})
        english_endings = rhyme_db.get('english_rhyme_endings', {})

        # 提取每行的韵脚
        rhyme_endings = []
        for line in lines:
            if not line.strip():
                continue

            words = line.strip().split()
            if not words:
                continue

            last_word = words[-1].lower().strip('.,!?;:"()[]')

            # 检测韵脚类型
            rhyme_type = self.detect_rhyme_type(last_word, chinese_endings, english_endings)
            rhyme_endings.append(rhyme_type)

        if len(rhyme_endings) < 4:
            return 0.0

        # 分析押韵模式
        rhyme_complexity = self.analyze_rhyme_pattern(rhyme_endings)

        self.logger.info(f"押韵分析: {len(rhyme_endings)}行, 复杂度{rhyme_complexity:.3f}")
        return rhyme_complexity

    def detect_rhyme_type(self, word, chinese_endings, english_endings):
        """
        检测词的韵脚类型

        Args:
            word: 词语
            chinese_endings: 中文韵脚数据库
            english_endings: 英文韵脚数据库

        Returns:
            str: 韵脚类型
        """
        # 检查中文韵脚
        for rhyme_type, endings in chinese_endings.items():
            if word in endings:
                return rhyme_type

        # 检查英文韵脚
        for rhyme_type, endings in english_endings.items():
            if word in endings:
                return rhyme_type

        # 如果没有匹配，使用词尾作为韵脚
        if len(word) >= 2:
            return word[-2:]  # 取后两个字符
        else:
            return word

    def analyze_rhyme_pattern(self, rhyme_endings):
        """
        分析押韵模式复杂度

        Args:
            rhyme_endings: 韵脚列表

        Returns:
            float: 押韵复杂度
        """
        if len(rhyme_endings) < 4:
            return 0.0

        # 计算不同韵脚的数量
        unique_rhymes = len(set(rhyme_endings))
        total_lines = len(rhyme_endings)

        # 检测常见押韵模式
        pattern_scores = {
            'AAAA': 0.2,  # 全部押韵，简单
            'AABB': 0.4,  # 连续押韵，较简单
            'ABAB': 0.6,  # 交替押韵，中等
            'ABCB': 0.6,  # 第二四行押韵，中等
            'ABBA': 0.8,  # 包围押韵，较复杂
            'ABCABC': 0.9,  # 三韵循环，复杂
            'ABCCBA': 1.0   # 回文押韵，最复杂
        }

        # 检测实际模式
        detected_pattern = self.detect_pattern(rhyme_endings[:8])  # 分析前8行

        if detected_pattern in pattern_scores:
            base_score = pattern_scores[detected_pattern]
        else:
            # 基于韵脚多样性计算
            diversity_ratio = unique_rhymes / total_lines
            base_score = min(diversity_ratio * 1.5, 1.0)

        # 考虑押韵密度
        rhyme_pairs = self.count_rhyme_pairs(rhyme_endings)
        density_bonus = min(rhyme_pairs / (total_lines // 2), 0.3)

        final_score = min(base_score + density_bonus, 1.0)

        self.logger.info(f"押韵模式: {detected_pattern}, 基础分{base_score:.3f}, 密度奖励{density_bonus:.3f}")
        return final_score

    def detect_pattern(self, rhyme_endings):
        """
        检测押韵模式

        Args:
            rhyme_endings: 韵脚列表

        Returns:
            str: 检测到的模式
        """
        if len(rhyme_endings) < 4:
            return "UNKNOWN"

        # 将韵脚转换为字母模式
        rhyme_map = {}
        pattern = []
        current_letter = 'A'

        for rhyme in rhyme_endings[:8]:  # 最多分析8行
            if rhyme not in rhyme_map:
                rhyme_map[rhyme] = current_letter
                current_letter = chr(ord(current_letter) + 1)
            pattern.append(rhyme_map[rhyme])

        pattern_str = ''.join(pattern)

        # 检测常见模式
        if len(pattern) >= 4:
            if pattern_str[:4] == 'AAAA':
                return 'AAAA'
            elif pattern_str[:4] == 'AABB':
                return 'AABB'
            elif pattern_str[:4] == 'ABAB':
                return 'ABAB'
            elif pattern_str[:4] == 'ABCB':
                return 'ABCB'
            elif pattern_str[:4] == 'ABBA':
                return 'ABBA'

        if len(pattern) >= 6:
            if pattern_str[:6] == 'ABCABC':
                return 'ABCABC'
            elif pattern_str[:6] == 'ABCCBA':
                return 'ABCCBA'

        return f"CUSTOM_{pattern_str[:4]}"

    def count_rhyme_pairs(self, rhyme_endings):
        """
        计算押韵对数量

        Args:
            rhyme_endings: 韵脚列表

        Returns:
            int: 押韵对数量
        """
        pairs = 0
        for i in range(len(rhyme_endings)):
            for j in range(i + 1, min(i + 4, len(rhyme_endings))):
                if rhyme_endings[i] == rhyme_endings[j]:
                    pairs += 1
        return pairs

    def calculate_metaphor_density_advanced(self, lyrics_text):
        """
        高级隐喻密度分析

        Args:
            lyrics_text: 歌词文本

        Returns:
            float: 隐喻密度 (每行隐喻数量)
        """
        # 加载隐喻数据库
        metaphor_db = self.load_database('metaphor_patterns.json')

        if not metaphor_db:
            self.logger.warning("隐喻数据库加载失败，使用简单分析")
            return self.calculate_metaphor_density(lyrics_text)

        indicators = metaphor_db.get('metaphor_indicators', {})
        patterns = metaphor_db.get('metaphor_patterns', {})
        synesthesia = metaphor_db.get('synesthesia_patterns', {})

        lines = lyrics_text.split('\n')
        total_metaphors = 0

        for line in lines:
            if not line.strip():
                continue

            line_lower = line.lower()
            line_metaphors = 0

            # 检测隐喻指示词
            for lang, words in indicators.items():
                for word in words:
                    line_metaphors += line_lower.count(word)

            # 检测隐喻模式
            for category, words in patterns.items():
                for word in words:
                    if word in line_lower:
                        line_metaphors += 0.5  # 隐喻词汇权重较低

            # 检测通感修辞
            for category, phrases in synesthesia.items():
                for phrase in phrases:
                    if phrase in line_lower:
                        line_metaphors += 1.5  # 通感修辞权重较高

            total_metaphors += line_metaphors

        # 计算每行平均隐喻数量
        metaphor_density = total_metaphors / len(lines) if len(lines) > 0 else 0.0

        self.logger.info(f"隐喻分析: {len(lines)}行, 总隐喻{total_metaphors:.1f}, 密度{metaphor_density:.4f}")
        return metaphor_density

    def calculate_international_appeal_advanced(self, lyrics_text, words):
        """
        高级国际传播潜力分析

        Args:
            lyrics_text: 歌词文本
            words: 词语列表

        Returns:
            float: 国际传播潜力 (0-1)
        """
        # 加载国际元素数据库
        intl_db = self.load_database('international_elements.json')

        if not intl_db:
            self.logger.warning("国际元素数据库加载失败，使用简单分析")
            # 简单的英文词汇比例
            english_words = sum(1 for word in words if word.isascii() and word.isalpha())
            return english_words / len(words) if len(words) > 0 else 0

        pop_elements = intl_db.get('international_pop_elements', {})
        fusion_indicators = intl_db.get('cultural_fusion_indicators', [])
        modern_slang = intl_db.get('modern_slang', [])

        total_score = 0.0
        max_score = 0.0

        lyrics_lower = lyrics_text.lower()

        # 1. 通用主题 (权重: 30%)
        theme_score = 0
        theme_words = pop_elements.get('universal_themes', [])
        for word in theme_words:
            if word.lower() in lyrics_lower:
                theme_score += 1
        theme_ratio = min(theme_score / 10, 1.0)  # 最多10个主题词
        total_score += theme_ratio * 0.3
        max_score += 0.3

        # 2. 全球城市和品牌 (权重: 15%)
        global_score = 0
        global_words = (pop_elements.get('global_cities', []) +
                       pop_elements.get('international_brands', []))
        for word in global_words:
            if word.lower() in lyrics_lower:
                global_score += 1
        global_ratio = min(global_score / 5, 1.0)  # 最多5个全球元素
        total_score += global_ratio * 0.15
        max_score += 0.15

        # 3. 现代科技和现象 (权重: 20%)
        tech_score = 0
        tech_words = (pop_elements.get('global_phenomena', []) +
                     pop_elements.get('technology_terms', []))
        for word in tech_words:
            if word.lower() in lyrics_lower:
                tech_score += 1
        tech_ratio = min(tech_score / 8, 1.0)  # 最多8个科技词汇
        total_score += tech_ratio * 0.2
        max_score += 0.2

        # 4. 跨文化符号 (权重: 15%)
        symbol_score = 0
        symbol_words = pop_elements.get('cross_cultural_symbols', [])
        for word in symbol_words:
            if word.lower() in lyrics_lower:
                symbol_score += 1
        symbol_ratio = min(symbol_score / 6, 1.0)  # 最多6个符号
        total_score += symbol_ratio * 0.15
        max_score += 0.15

        # 5. 国际音乐术语 (权重: 10%)
        music_score = 0
        music_words = pop_elements.get('international_music_terms', [])
        for word in music_words:
            if word.lower() in lyrics_lower:
                music_score += 1
        music_ratio = min(music_score / 5, 1.0)  # 最多5个音乐术语
        total_score += music_ratio * 0.1
        max_score += 0.1

        # 6. 现代俚语和情感表达 (权重: 10%)
        slang_score = 0
        all_slang = (pop_elements.get('global_emotions', []) + modern_slang)
        for word in all_slang:
            if word.lower() in lyrics_lower:
                slang_score += 1
        slang_ratio = min(slang_score / 5, 1.0)  # 最多5个俚语
        total_score += slang_ratio * 0.1
        max_score += 0.1

        # 计算最终得分
        final_score = total_score / max_score if max_score > 0 else 0

        self.logger.info(f"国际传播潜力分析:")
        self.logger.info(f"  通用主题: {theme_ratio:.3f} (权重30%)")
        self.logger.info(f"  全球元素: {global_ratio:.3f} (权重15%)")
        self.logger.info(f"  科技现象: {tech_ratio:.3f} (权重20%)")
        self.logger.info(f"  跨文化符号: {symbol_ratio:.3f} (权重15%)")
        self.logger.info(f"  音乐术语: {music_ratio:.3f} (权重10%)")
        self.logger.info(f"  现代俚语: {slang_ratio:.3f} (权重10%)")
        self.logger.info(f"  最终得分: {final_score:.4f}")

        return final_score

    def reset(self):
        """重置插件状态"""
        try:
            # 清空参数
            self.parameters = {}

            # 清空缓存（如果有）
            if hasattr(self, 'cache'):
                self.cache = {}

            # 清空数据库缓存
            if hasattr(self, '_db_cache'):
                self._db_cache = {}

            self.logger.info("歌词分析插件状态已重置")

        except Exception as e:
            self.logger.error(f"重置歌词分析插件失败: {e}")