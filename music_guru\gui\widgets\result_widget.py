#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
结果显示控件

MIT License
Copyright (c) 2025 Music Guru
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QProgressBar, QGroupBox, QScrollArea)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont, QPalette, QColor


class ScoreBar(QProgressBar):
    """分数进度条"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setRange(0, 100)
        self.setValue(0)
        self.setTextVisible(True)
        self.setMinimumHeight(30)
        
        # 设置样式
        self.setStyleSheet("""
            QProgressBar {
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
                background-color: #f0f0f0;
            }
            
            QProgressBar::chunk {
                background-color: #4CAF50;
                width: 10px;
                margin: 0.5px;
            }
        """)
    
    def set_score(self, score):
        """设置分数"""
        value = min(max(int(score), 0), 100)
        self.setValue(value)
        self.setFormat(f"{score:.1f} 分")
        
        # 根据分数设置颜色
        if score >= 80:
            color = "#4CAF50"  # 绿色
        elif score >= 60:
            color = "#FFC107"  # 黄色
        else:
            color = "#F44336"  # 红色
        
        self.setStyleSheet(f"""
            QProgressBar {{
                border: 2px solid grey;
                border-radius: 5px;
                text-align: center;
                background-color: #f0f0f0;
            }}
            
            QProgressBar::chunk {{
                background-color: {color};
                width: 10px;
                margin: 0.5px;
            }}
        """)


class ResultWidget(QWidget):
    """结果显示控件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 总分显示
        self.score_label = QLabel("总分")
        self.score_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.score_label.setFont(QFont("Arial", 16, QFont.Weight.Bold))
        layout.addWidget(self.score_label)
        
        self.score_bar = ScoreBar()
        layout.addWidget(self.score_bar)
        
        # 维度分数显示
        self.dimension_group = QGroupBox("维度分数")
        self.dimension_layout = QVBoxLayout(self.dimension_group)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setWidget(self.dimension_group)
        
        layout.addWidget(scroll_area)
        layout.setStretchFactor(scroll_area, 1)
    
    def set_score(self, score):
        """设置总分"""
        self.score_label.setText(f"总分: {score:.1f}")
        self.score_bar.set_score(score)
    
    def set_dimension_scores(self, dimension_scores):
        """设置维度分数"""
        # 清空现有内容
        for i in reversed(range(self.dimension_layout.count())):
            widget = self.dimension_layout.itemAt(i).widget()
            if widget:
                widget.deleteLater()
        
        # 维度名称映射
        dimension_names = {
            "technical": "技术创作",
            "emotional": "情感表达",
            "market": "市场传播",
            "cultural": "文化创新",
            "production": "制作工程"
        }
        
        # 添加新的维度分数
        for dimension, score in dimension_scores.items():
            # 创建维度标签
            dim_name = dimension_names.get(dimension, dimension)
            dim_label = QLabel(f"{dim_name}: {score:.1f}")
            dim_label.setFont(QFont("Arial", 12))
            
            # 创建分数条
            score_bar = ScoreBar()
            score_bar.set_score(score)
            
            # 添加到布局
            self.dimension_layout.addWidget(dim_label)
            self.dimension_layout.addWidget(score_bar)
            self.dimension_layout.addSpacing(10)
