#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
音频格式插件基类

MIT License
Copyright (c) 2025 Music Guru
"""

from abc import ABC, abstractmethod
import os
import logging
from typing import Tuple, Optional, Dict, Any, List


class AudioFormatPlugin(ABC):
    """音频格式插件基类"""

    def __init__(self):
        """初始化音频格式插件"""
        self.name = "基础音频格式插件"
        self.description = "音频格式插件基类"
        self.extensions = []  # 支持的文件扩展名列表
        self.mime_types = []  # 支持的MIME类型列表
        self.versions = []    # 支持的版本列表
        self.enabled = True
        self.priority = 0     # 插件优先级，数字越大优先级越高
        self.logger = logging.getLogger(f"AudioFormatPlugin.{self.__class__.__name__}")

        # 加载后端库
        self.backends = self._load_backends()

    def _load_backends(self) -> Dict[str, bool]:
        """
        加载后端库

        Returns:
            Dict[str, bool]: 后端库加载状态字典
        """
        return {}

    @abstractmethod
    def can_handle(self, file_path: str) -> bool:
        """
        检查是否可以处理指定文件

        Args:
            file_path: 音频文件路径

        Returns:
            bool: 是否可以处理
        """
        pass

    @abstractmethod
    def load_audio(self, file_path: str) -> Tuple[Optional[Any], Optional[int]]:
        """
        加载音频文件

        Args:
            file_path: 音频文件路径

        Returns:
            Tuple[Optional[Any], Optional[int]]: 音频数据和采样率
        """
        pass

    @abstractmethod
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        获取音频元数据

        Args:
            file_path: 音频文件路径

        Returns:
            Dict[str, Any]: 元数据字典
        """
        pass

    @abstractmethod
    def get_lyrics(self, file_path: str) -> Optional[str]:
        """
        获取歌词

        Args:
            file_path: 音频文件路径

        Returns:
            Optional[str]: 歌词文本
        """
        pass

    def get_file_extension(self, file_path: str) -> str:
        """
        获取文件扩展名

        Args:
            file_path: 文件路径

        Returns:
            str: 文件扩展名（小写，不包含点）
        """
        _, ext = os.path.splitext(file_path)
        return ext.lower().lstrip('.')

    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        获取文件信息

        Args:
            file_path: 文件路径

        Returns:
            Dict[str, Any]: 文件信息字典
        """
        info = {
            "file_path": file_path,
            "file_name": os.path.basename(file_path),
            "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
            "file_extension": self.get_file_extension(file_path),
            "can_handle": self.can_handle(file_path)
        }

        return info

    def get_version(self, file_path: str) -> str:
        """
        获取文件版本

        Args:
            file_path: 文件路径

        Returns:
            str: 文件版本
        """
        return "unknown"

    def get_supported_backends(self) -> List[str]:
        """
        获取支持的后端库列表

        Returns:
            List[str]: 后端库列表
        """
        return [backend for backend, loaded in self.backends.items() if loaded]

    def is_backend_available(self, backend_name: str) -> bool:
        """
        检查后端库是否可用

        Args:
            backend_name: 后端库名称

        Returns:
            bool: 是否可用
        """
        return self.backends.get(backend_name, False)


class AudioFormatManager:
    """音频格式管理器"""

    def __init__(self):
        """初始化音频格式管理器"""
        self.plugins = {}  # 插件字典，键为插件ID
        self.extension_map = {}  # 扩展名到插件ID的映射
        self.mime_type_map = {}  # MIME类型到插件ID的映射
        self.logger = logging.getLogger("AudioFormatManager")

    def register_plugin(self, plugin_id: str, plugin: AudioFormatPlugin):
        """
        注册插件

        Args:
            plugin_id: 插件ID
            plugin: 插件实例
        """
        self.plugins[plugin_id] = plugin
        self.logger.info(f"注册插件: {plugin_id} ({plugin.name})")

        # 更新扩展名映射
        for ext in plugin.extensions:
            if ext not in self.extension_map:
                self.extension_map[ext] = []
            self.extension_map[ext].append(plugin_id)
            self.logger.debug(f"映射扩展名 {ext} 到插件 {plugin_id}")

        # 更新MIME类型映射
        for mime_type in plugin.mime_types:
            if mime_type not in self.mime_type_map:
                self.mime_type_map[mime_type] = []
            self.mime_type_map[mime_type].append(plugin_id)
            self.logger.debug(f"映射MIME类型 {mime_type} 到插件 {plugin_id}")

    def get_plugin_for_file(self, file_path: str) -> Optional[AudioFormatPlugin]:
        """
        获取可以处理指定文件的插件

        Args:
            file_path: 音频文件路径

        Returns:
            Optional[AudioFormatPlugin]: 插件实例，如果没有找到则返回None
        """
        if not os.path.exists(file_path):
            self.logger.warning(f"文件不存在: {file_path}")
            return None

        # 获取文件扩展名
        ext = os.path.splitext(file_path)[1].lower().lstrip('.')

        # 创建候选插件列表
        candidates = []

        # 添加支持该扩展名的插件
        if ext in self.extension_map:
            for plugin_id in self.extension_map[ext]:
                plugin = self.plugins.get(plugin_id)
                if plugin and plugin.enabled:
                    candidates.append((plugin, plugin.priority))

        # 如果没有找到匹配的插件，添加所有插件
        if not candidates:
            for plugin in self.plugins.values():
                if plugin.enabled:
                    candidates.append((plugin, plugin.priority - 100))  # 降低优先级

        # 按优先级排序
        candidates.sort(key=lambda x: x[1], reverse=True)

        # 尝试每个候选插件
        for plugin, _ in candidates:
            try:
                if plugin.can_handle(file_path):
                    self.logger.info(f"使用插件 {plugin.__class__.__name__} 处理文件: {os.path.basename(file_path)}")
                    return plugin
            except Exception as e:
                self.logger.error(f"插件 {plugin.__class__.__name__} 处理文件失败: {e}")

        self.logger.warning(f"没有找到可以处理文件的插件: {os.path.basename(file_path)}")
        return None

    def get_supported_extensions(self) -> list:
        """
        获取所有支持的文件扩展名

        Returns:
            list: 扩展名列表
        """
        return list(self.extension_map.keys())

    def get_supported_mime_types(self) -> list:
        """
        获取所有支持的MIME类型

        Returns:
            list: MIME类型列表
        """
        return list(self.mime_type_map.keys())

    def get_file_filter(self) -> str:
        """
        获取文件过滤器字符串（用于文件对话框）

        Returns:
            str: 文件过滤器字符串
        """
        filters = []

        # 添加所有支持的格式
        all_exts = self.get_supported_extensions()
        if all_exts:
            exts_str = " ".join(f"*.{ext}" for ext in all_exts)
            filters.append(f"所有支持的音频格式 ({exts_str})")

        # 添加每种格式
        for plugin in self.plugins.values():
            if plugin.enabled and plugin.extensions:
                exts_str = " ".join(f"*.{ext}" for ext in plugin.extensions)
                filters.append(f"{plugin.name} ({exts_str})")

        # 添加所有文件
        filters.append("所有文件 (*.*)")

        return ";;".join(filters)

    def get_plugin_info(self) -> List[Dict[str, Any]]:
        """
        获取所有插件信息

        Returns:
            List[Dict[str, Any]]: 插件信息列表
        """
        info = []
        for plugin_id, plugin in self.plugins.items():
            plugin_info = {
                "id": plugin_id,
                "name": plugin.name,
                "description": plugin.description,
                "extensions": plugin.extensions,
                "mime_types": plugin.mime_types,
                "versions": plugin.versions,
                "enabled": plugin.enabled,
                "priority": plugin.priority,
                "backends": plugin.get_supported_backends()
            }
            info.append(plugin_info)

        return info

    def test_file(self, file_path: str) -> Dict[str, Any]:
        """
        测试文件，获取所有可以处理该文件的插件

        Args:
            file_path: 文件路径

        Returns:
            Dict[str, Any]: 测试结果
        """
        if not os.path.exists(file_path):
            return {"error": "文件不存在"}

        result = {
            "file_path": file_path,
            "file_name": os.path.basename(file_path),
            "file_size": os.path.getsize(file_path),
            "file_extension": os.path.splitext(file_path)[1].lower().lstrip('.'),
            "plugins": []
        }

        # 测试每个插件
        for plugin_id, plugin in self.plugins.items():
            if not plugin.enabled:
                continue

            try:
                can_handle = plugin.can_handle(file_path)
                plugin_result = {
                    "id": plugin_id,
                    "name": plugin.name,
                    "can_handle": can_handle
                }

                if can_handle:
                    # 获取版本
                    plugin_result["version"] = plugin.get_version(file_path)

                    # 获取元数据
                    try:
                        metadata = plugin.get_metadata(file_path)
                        plugin_result["metadata"] = metadata
                    except Exception as e:
                        plugin_result["metadata_error"] = str(e)

                    # 检查是否有歌词
                    try:
                        lyrics = plugin.get_lyrics(file_path)
                        plugin_result["has_lyrics"] = lyrics is not None
                    except Exception as e:
                        plugin_result["lyrics_error"] = str(e)

                result["plugins"].append(plugin_result)
            except Exception as e:
                result["plugins"].append({
                    "id": plugin_id,
                    "name": plugin.name,
                    "error": str(e)
                })

        return result
