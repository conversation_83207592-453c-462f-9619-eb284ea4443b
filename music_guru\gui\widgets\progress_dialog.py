#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
进度对话框

MIT License
Copyright (c) 2025 Music Guru
"""

import time
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QProgressBar, QPushButton, QDialogButtonBox)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal


class ProgressDialog(QDialog):
    """进度对话框"""
    
    # 自定义信号
    canceled = pyqtSignal()
    
    def __init__(self, parent=None, title="处理中", max_value=100):
        super().__init__(parent)
        
        self.setWindowTitle(title)
        self.setMinimumWidth(400)
        self.setModal(True)
        
        # 初始化变量
        self.max_value = max_value
        self.current_value = 0
        self.start_time = None
        self.last_update_time = None
        self.estimated_total_time = None
        
        # 创建UI
        self.init_ui()
        
        # 创建定时器，用于更新剩余时间
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_remaining_time)
        self.timer.start(1000)  # 每秒更新一次
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 状态标签
        self.status_label = QLabel("准备中...")
        layout.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, self.max_value)
        self.progress_bar.setValue(0)
        layout.addWidget(self.progress_bar)
        
        # 时间信息
        time_layout = QHBoxLayout()
        
        self.elapsed_time_label = QLabel("已用时间: 00:00:00")
        time_layout.addWidget(self.elapsed_time_label)
        
        self.remaining_time_label = QLabel("剩余时间: 计算中...")
        time_layout.addWidget(self.remaining_time_label)
        
        layout.addLayout(time_layout)
        
        # 详细信息
        self.detail_label = QLabel("")
        layout.addWidget(self.detail_label)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Cancel)
        button_box.rejected.connect(self.on_cancel)
        layout.addWidget(button_box)
    
    def set_max_value(self, max_value):
        """设置最大值"""
        self.max_value = max_value
        self.progress_bar.setRange(0, max_value)
    
    def set_value(self, value, status=None, detail=None):
        """设置当前值"""
        # 如果是第一次更新，记录开始时间
        if self.start_time is None:
            self.start_time = time.time()
            self.last_update_time = self.start_time
        
        # 更新当前值
        self.current_value = value
        self.progress_bar.setValue(value)
        
        # 更新状态文本
        if status:
            self.status_label.setText(status)
        
        # 更新详细信息
        if detail:
            self.detail_label.setText(detail)
        
        # 计算已用时间
        elapsed_time = time.time() - self.start_time
        elapsed_str = str(timedelta(seconds=int(elapsed_time)))
        self.elapsed_time_label.setText(f"已用时间: {elapsed_str}")
        
        # 计算预计总时间
        if value > 0:
            # 使用移动平均法计算预计总时间
            current_time = time.time()
            time_per_unit = elapsed_time / value
            
            if self.estimated_total_time is None:
                self.estimated_total_time = time_per_unit * self.max_value
            else:
                # 给新的估计一个较小的权重
                alpha = 0.2
                new_estimate = time_per_unit * self.max_value
                self.estimated_total_time = (1 - alpha) * self.estimated_total_time + alpha * new_estimate
            
            # 更新最后更新时间
            self.last_update_time = current_time
        
        # 更新UI
        self.update_remaining_time()
    
    def update_remaining_time(self):
        """更新剩余时间"""
        if self.start_time is None:
            return
        
        # 计算已用时间
        elapsed_time = time.time() - self.start_time
        elapsed_str = str(timedelta(seconds=int(elapsed_time)))
        self.elapsed_time_label.setText(f"已用时间: {elapsed_str}")
        
        # 计算剩余时间
        if self.current_value > 0 and self.estimated_total_time is not None:
            remaining_time = self.estimated_total_time - elapsed_time
            if remaining_time < 0:
                remaining_time = 0
            
            remaining_str = str(timedelta(seconds=int(remaining_time)))
            self.remaining_time_label.setText(f"剩余时间: {remaining_str}")
        else:
            self.remaining_time_label.setText("剩余时间: 计算中...")
    
    def on_cancel(self):
        """取消按钮点击事件"""
        self.canceled.emit()
        self.reject()
    
    def closeEvent(self, event):
        """关闭事件"""
        self.timer.stop()
        super().closeEvent(event)
