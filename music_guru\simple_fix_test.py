#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的修复效果测试
"""

import os
import sys

print("开始简单测试...")

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_lyrics_analysis():
    """测试歌词分析差异化"""
    print("\n=== 测试歌词分析差异化 ===")
    
    try:
        from plugins.emotional.lyrics_analysis import LyricsAnalysisPlugin
        from core.data_model import MusicData
        
        plugin = LyricsAnalysisPlugin()
        
        # 测试不同的歌词
        test_cases = [
            {
                "title": "中文流行歌曲",
                "lyrics": """
                你是我心中的太阳
                照亮我前进的方向
                像春风一样温暖
                如星光一样闪亮
                爱情就像一首歌
                唱出我们的快乐
                """
            },
            {
                "title": "英文歌曲",
                "lyrics": """
                Love is like a butterfly
                Flying high up in the sky
                Beautiful and free
                Dancing gracefully
                You are my sunshine
                Making everything bright
                """
            },
            {
                "title": "简单歌词",
                "lyrics": """
                我爱你
                你爱我
                我们在一起
                很快乐
                """
            }
        ]
        
        results = []
        
        for case in test_cases:
            print(f"\n测试: {case['title']}")
            
            # 创建音乐数据
            data = MusicData()
            data.title = case['title']
            data.lyrics = case['lyrics'].strip()
            
            # 评价
            result = plugin.evaluate(data)
            score = result.get("score", 0)
            
            print(f"评分: {score:.2f}")
            results.append(score)
            
            # 显示详细信息
            details = result.get("details", {})
            for key, detail in details.items():
                print(f"  {key}: {detail}")
        
        # 分析差异
        print(f"\n=== 结果分析 ===")
        max_score = max(results)
        min_score = min(results)
        print(f"最高分: {max_score:.2f}")
        print(f"最低分: {min_score:.2f}")
        print(f"分数差异: {max_score - min_score:.2f}")
        
        if max_score - min_score > 5:
            print("✅ 歌词分析已实现差异化评价")
        else:
            print("❌ 歌词分析仍然缺乏差异化")
            
    except Exception as e:
        print(f"❌ 歌词分析测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_audio_analyzer():
    """测试音频分析器"""
    print("\n=== 测试音频分析器 ===")
    
    try:
        from utils.audio_analyzer import AudioAnalyzer
        import numpy as np
        
        analyzer = AudioAnalyzer()
        
        # 创建模拟音频数据
        sample_rate = 44100
        duration = 3.0  # 3秒
        t = np.linspace(0, duration, int(sample_rate * duration))
        
        # 测试不同的音频信号
        test_signals = [
            {
                "name": "正弦波440Hz",
                "signal": np.sin(2 * np.pi * 440 * t)
            },
            {
                "name": "复合信号",
                "signal": np.sin(2 * np.pi * 440 * t) + 0.5 * np.sin(2 * np.pi * 880 * t)
            },
            {
                "name": "白噪声",
                "signal": np.random.normal(0, 0.1, len(t))
            }
        ]
        
        for test in test_signals:
            print(f"\n测试信号: {test['name']}")
            
            analyzer.audio_data = test['signal']
            analyzer.sample_rate = sample_rate
            analyzer.duration = duration
            
            # 测试各种分析功能
            try:
                dr = analyzer.analyze_dynamic_range()
                print(f"  动态范围: {dr:.2f}dB")
                
                nf = analyzer.analyze_noise_floor()
                print(f"  底噪电平: {nf:.2f}dBFS")
                
                iht = analyzer.analyze_intro_hook_timing()
                print(f"  前奏峰值: {iht:.2f}秒")
                
                cr = analyzer.analyze_chorus_range()
                print(f"  音域范围: {cr:.2f}度")
                
                # 测试新功能
                harmony = analyzer.analyze_harmony_features()
                if harmony:
                    print(f"  和声特征: 公式化={harmony.get('formula_usage', 0):.3f}")
                
                melody = analyzer.analyze_melody_features()
                if melody:
                    print(f"  旋律特征: 音程跨度={melody.get('interval_range', 0):.1f}")
                
                print("✅ 音频分析功能正常")
                
            except Exception as e:
                print(f"❌ 音频分析失败: {e}")
        
    except Exception as e:
        print(f"❌ 音频分析器测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_batch_processor():
    """测试批量处理器的改进"""
    print("\n=== 测试批量处理器改进 ===")
    
    try:
        from utils.batch_processor import BatchProcessor
        
        processor = BatchProcessor()
        
        # 测试歌词分析方法
        test_lines = [
            "你是我心中的太阳光",
            "照亮我前进的方向向",
            "像春风一样温暖暖",
            "如星光一样闪亮亮"
        ]
        
        rhyme_score = processor.analyze_simple_rhyme_scheme(test_lines)
        print(f"押韵复杂度: {rhyme_score:.3f}")
        
        test_lyrics = "你像春风一样温暖，如星光一样闪亮，仿佛天使在歌唱"
        metaphor_score = processor.analyze_simple_metaphor_density(test_lyrics)
        print(f"隐喻密度: {metaphor_score:.3f}")
        
        print("✅ 批量处理器改进功能正常")
        
    except Exception as e:
        print(f"❌ 批量处理器测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("开始修复效果验证...")
    
    # 测试各个组件
    test_lyrics_analysis()
    test_audio_analyzer()
    test_batch_processor()
    
    print(f"\n{'='*50}")
    print("测试完成")
    print(f"{'='*50}")
    
    print("\n📋 修复总结:")
    print("1. ✅ 增强了音频分析功能，添加了和声、旋律、流媒体分析")
    print("2. ✅ 修复了歌词分析插件，现在能基于实际歌词计算参数")
    print("3. ✅ 改进了批量处理器，增加了详细的日志和错误处理")
    print("4. ✅ 添加了歌词差异化分析方法")
    
    print("\n🎯 下一步:")
    print("1. 重启Music Guru程序以加载新的插件代码")
    print("2. 运行批量处理测试，验证评分差异化")
    print("3. 检查日志确认音频分析和歌词分析正常工作")
