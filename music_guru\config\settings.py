#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
全局设置

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import json


class Settings:
    """全局设置类"""
    
    def __init__(self):
        """初始化设置"""
        self.app_name = "Music Guru"
        self.version = "1.0.0"
        self.config_file = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                        "config", "settings.json")
        
        # 维度权重设置
        self.dimension_weights = {
            "technical": 0.30,  # 技术创作维度权重 30%
            "emotional": 0.25,  # 情感表达维度权重 25%
            "market": 0.20,     # 市场传播维度权重 20%
            "cultural": 0.15,   # 文化创新维度权重 15%
            "production": 0.10  # 制作工程维度权重 10%
        }
        
        # 动态校准机制
        self.calibration = {
            "short_video_factor": 1.5,  # 短视频时代对前奏吸引力权重
            "album_concept_factor": 0.7,  # 专辑概念完整性权重
            "alt_pop_ai_threshold": 0.3,  # 另类流行乐AI生成内容占比阈值
            "cultural_market_boost": 0.05  # 文化创新高分时市场传播权重提升
        }
        
        # 加载配置文件
        self.load_settings()
    
    def load_settings(self):
        """从配置文件加载设置"""
        if os.path.exists(self.config_file):
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    
                    # 更新设置
                    if 'dimension_weights' in settings:
                        self.dimension_weights.update(settings['dimension_weights'])
                    
                    if 'calibration' in settings:
                        self.calibration.update(settings['calibration'])
            except Exception as e:
                print(f"加载设置文件失败: {e}")
    
    def save_settings(self):
        """保存设置到配置文件"""
        settings = {
            'dimension_weights': self.dimension_weights,
            'calibration': self.calibration
        }
        
        try:
            os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=4, ensure_ascii=False)
        except Exception as e:
            print(f"保存设置文件失败: {e}")
    
    def get_dimension_weight(self, dimension):
        """获取维度权重"""
        return self.dimension_weights.get(dimension, 0.0)
    
    def set_dimension_weight(self, dimension, weight):
        """设置维度权重"""
        if dimension in self.dimension_weights:
            self.dimension_weights[dimension] = weight
            self.save_settings()
