#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级歌词分析功能
"""

import os
import sys
import logging

print("开始测试高级歌词分析功能...")

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_advanced_lyrics_analysis():
    """测试高级歌词分析功能"""
    print("\n=== 测试高级歌词分析功能 ===")
    
    # 设置详细日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    try:
        from plugins.emotional.lyrics_analysis import LyricsAnalysisPlugin
        from core.data_model import MusicData
        
        plugin = LyricsAnalysisPlugin()
        
        # 测试不同类型的歌词
        test_cases = [
            {
                "title": "简单中文歌词",
                "lyrics": """
                我爱你
                你爱我
                我们在一起
                很快乐
                """
            },
            {
                "title": "复杂中文歌词（有押韵和隐喻）",
                "lyrics": """
                你像春风一样温暖
                如星光一样闪亮
                仿佛天使在歌唱
                带给我无限希望
                爱情如火般燃烧
                心中的花朵绽放
                时光如水般流淌
                永远不会被遗忘
                """
            },
            {
                "title": "国际化歌词（中英混合）",
                "lyrics": """
                Love is like a butterfly
                Flying high up in the sky
                你是我心中的star
                Beautiful and free
                Dancing gracefully
                在New York的街道上
                我们share着同一个dream
                用iPhone记录每个moment
                这是我们的story
                Amazing and incredible
                """
            },
            {
                "title": "现代流行歌词（含热词和俚语）",
                "lyrics": """
                在这个AI的时代
                我们用social media连接
                每天刷着短视频
                点赞转发很忙碌
                emo的时候听音乐
                治愈系的melody
                这很cool这很awesome
                我们都是digital native
                追求work-life balance
                享受每个vibe和mood
                """
            }
        ]
        
        print(f"\n{'='*80}")
        print("高级歌词分析测试结果")
        print(f"{'='*80}")
        
        for i, case in enumerate(test_cases):
            print(f"\n--- 测试案例 {i+1}: {case['title']} ---")
            print(f"歌词内容:")
            print(case['lyrics'].strip())
            print(f"\n分析结果:")
            
            # 创建音乐数据
            data = MusicData()
            data.title = case['title']
            data.lyrics = case['lyrics'].strip()
            
            # 评价
            result = plugin.evaluate(data)
            score = result.get("score", 0)
            details = result.get("details", {})
            
            print(f"最终得分: {score:.1f}/100")
            print(f"详细评分:")
            for key, detail in details.items():
                print(f"  {key}: {detail}")
        
        print(f"\n✅ 高级歌词分析测试完成")
        
    except Exception as e:
        print(f"❌ 高级歌词分析测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_database_loading():
    """测试数据库加载功能"""
    print("\n=== 测试数据库加载功能 ===")
    
    try:
        from plugins.emotional.lyrics_analysis import LyricsAnalysisPlugin
        
        plugin = LyricsAnalysisPlugin()
        
        # 测试各个数据库文件
        databases = [
            'metaphor_patterns.json',
            'international_elements.json', 
            'rhyme_patterns.json',
            'trending_words_2024.json'
        ]
        
        for db_name in databases:
            print(f"\n测试数据库: {db_name}")
            db_data = plugin.load_database(db_name)
            
            if db_data:
                print(f"✅ 加载成功，包含 {len(db_data)} 个主要类别")
                for key in list(db_data.keys())[:3]:  # 显示前3个键
                    print(f"  - {key}")
                if len(db_data) > 3:
                    print(f"  ... 还有 {len(db_data) - 3} 个类别")
            else:
                print(f"❌ 加载失败")
        
        print(f"\n✅ 数据库加载测试完成")
        
    except Exception as e:
        print(f"❌ 数据库加载测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_individual_functions():
    """测试各个分析函数"""
    print("\n=== 测试各个分析函数 ===")
    
    try:
        from plugins.emotional.lyrics_analysis import LyricsAnalysisPlugin
        
        plugin = LyricsAnalysisPlugin()
        
        # 测试押韵分析
        print("\n1. 测试押韵分析:")
        test_lines = [
            "你像春风一样温暖",
            "如星光一样闪亮", 
            "仿佛天使在歌唱",
            "带给我无限希望"
        ]
        rhyme_score = plugin.calculate_rhyme_scheme_advanced(test_lines)
        print(f"押韵复杂度: {rhyme_score:.4f}")
        
        # 测试隐喻分析
        print("\n2. 测试隐喻分析:")
        test_lyrics = "你像春风一样温暖，如星光一样闪亮，仿佛天使在歌唱，爱情如火般燃烧"
        metaphor_score = plugin.calculate_metaphor_density_advanced(test_lyrics)
        print(f"隐喻密度: {metaphor_score:.4f}")
        
        # 测试国际传播潜力分析
        print("\n3. 测试国际传播潜力分析:")
        test_lyrics_intl = "Love is amazing, we share the same dream in New York, using iPhone to capture every moment"
        test_words = test_lyrics_intl.split()
        intl_score = plugin.calculate_international_appeal_advanced(test_lyrics_intl, test_words)
        print(f"国际传播潜力: {intl_score:.4f}")
        
        print(f"\n✅ 各个分析函数测试完成")
        
    except Exception as e:
        print(f"❌ 分析函数测试失败: {e}")
        import traceback
        traceback.print_exc()

def analyze_expected_improvements():
    """分析预期改进效果"""
    print(f"\n{'='*80}")
    print("预期改进效果分析")
    print(f"{'='*80}")
    
    print("\n📊 修复前后对比:")
    print("修复前:")
    print("  - 押韵复杂度: 0.000 (无法检测)")
    print("  - 隐喻密度: 0.000 (无法检测)")
    print("  - 国际传播潜力: 0.000 (仅基于英文单词数)")
    print("  - 词汇丰富度: 1.000 (计算错误)")
    
    print("\n修复后预期:")
    print("  - 押韵复杂度: 0.2-1.0 (基于实际押韵模式)")
    print("  - 隐喻密度: 0.1-2.0 (基于隐喻数据库)")
    print("  - 国际传播潜力: 0.1-0.8 (基于多维度国际元素)")
    print("  - 词汇丰富度: 0.3-0.8 (正确的TTR计算)")
    
    print("\n🎯 关键改进:")
    print("✅ 1. 只使用实际歌词，禁止固定文本")
    print("✅ 2. 基于数据库的隐喻识别")
    print("✅ 3. 多维度国际传播潜力评估")
    print("✅ 4. 高级押韵模式检测")
    print("✅ 5. 完整的评分追踪日志")
    
    print("\n📋 数据库支持:")
    print("✅ metaphor_patterns.json - 隐喻指示词和模式")
    print("✅ international_elements.json - 国际流行元素")
    print("✅ rhyme_patterns.json - 押韵模式和韵脚")
    print("✅ trending_words_2024.json - 年度热词")
    
    print("\n🔍 评分差异化预期:")
    print("- 简单歌词: 20-35分 (基础词汇，无复杂修辞)")
    print("- 中等歌词: 45-65分 (有押韵或隐喻)")
    print("- 复杂歌词: 70-85分 (丰富修辞，复杂押韵)")
    print("- 国际化歌词: 75-90分 (国际元素丰富)")

if __name__ == "__main__":
    print("开始高级歌词分析功能验证...")
    
    # 测试数据库加载
    test_database_loading()
    
    # 测试各个分析函数
    test_individual_functions()
    
    # 测试完整的歌词分析
    test_advanced_lyrics_analysis()
    
    # 分析预期改进效果
    analyze_expected_improvements()
    
    print(f"\n{'='*80}")
    print("高级歌词分析测试完成")
    print(f"{'='*80}")
    
    print("\n📋 修复总结:")
    print("1. ✅ 实现只使用实际歌词的策略")
    print("2. ✅ 创建完整的数据库支持系统")
    print("3. ✅ 实现高级押韵模式检测")
    print("4. ✅ 实现基于数据库的隐喻识别")
    print("5. ✅ 实现多维度国际传播潜力评估")
    print("6. ✅ 添加详细的评分追踪日志")
    
    print("\n🎯 下一步:")
    print("1. 重启Music Guru程序以加载新的分析逻辑")
    print("2. 运行实际音乐文件测试，验证评分差异化")
    print("3. 检查日志确认数据库加载和分析过程正确")
    print("4. 根据需要补充和完善数据库内容")
