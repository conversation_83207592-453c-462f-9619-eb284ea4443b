#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
旋律结构插件

MIT License
Copyright (c) 2025 Music Guru
"""

from core.plugin_manager import Plugin


class MelodyStructurePlugin(Plugin):
    """旋律结构插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "旋律结构"
        self.description = "评价旋律结构（音程跨度/记忆点密度）"
        self.dimension = "technical"
        self.category = "melody_structure"
        
        # 参数设置
        self.parameters = {
            "interval_range": 0,  # 音程跨度
            "hook_density": 0,    # 记忆点密度
            "reverse_melody": False  # 逆向旋律发展
        }
    
    def evaluate(self, data):
        """
        评价旋律结构
        
        评分标准：
        - 主歌到副歌音程跨度＞四度（+2分）
        - Hook重复间隔＜45秒（+3分）
        - 采用逆向旋律发展（+1.5分）
        
        满分：6.5分
        """
        score = 0
        details = {}
        
        # 获取参数
        interval_range = data.technical_data.get("melody_structure", {}).get("interval_range", 0)
        hook_density = data.technical_data.get("melody_structure", {}).get("hook_density", 0)
        reverse_melody = data.technical_data.get("melody_structure", {}).get("reverse_melody", False)
        
        # 评分计算
        # 1. 主歌到副歌音程跨度＞四度（+2分）
        if interval_range > 4:
            score += 2
            details["interval_range"] = "主歌到副歌音程跨度大于四度，+2分"
        else:
            details["interval_range"] = "主歌到副歌音程跨度不足四度，+0分"
        
        # 2. Hook重复间隔＜45秒（+3分）
        if hook_density > 0 and hook_density < 45:
            score += 3
            details["hook_density"] = f"Hook重复间隔为{hook_density}秒，小于45秒，+3分"
        else:
            details["hook_density"] = f"Hook重复间隔为{hook_density}秒，不符合要求，+0分"
        
        # 3. 采用逆向旋律发展（+1.5分）
        if reverse_melody:
            score += 1.5
            details["reverse_melody"] = "采用逆向旋律发展，+1.5分"
        else:
            details["reverse_melody"] = "未采用逆向旋律发展，+0分"
        
        # 标准化分数到100分制
        normalized_score = (score / 6.5) * 100
        
        return {
            "score": normalized_score,
            "details": details
        }
