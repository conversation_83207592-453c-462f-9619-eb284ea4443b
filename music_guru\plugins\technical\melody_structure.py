#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
旋律结构插件

MIT License
Copyright (c) 2025 Music Guru
"""

from core.plugin_manager import Plugin


class MelodyStructurePlugin(Plugin):
    """旋律结构插件"""

    def __init__(self):
        super().__init__()
        self.name = "旋律结构"
        self.description = "评价旋律结构（音程跨度/记忆点密度）"
        self.dimension = "technical"
        self.category = "melody_structure"

        # 参数设置
        self.parameters = {
            "interval_range": 0,  # 音程跨度
            "hook_density": 0,    # 记忆点密度
            "reverse_melody": False  # 逆向旋律发展
        }

    def evaluate(self, data):
        """
        评价旋律结构

        评分标准（参考大模型评价）：
        - 音程跨度：>10度优秀，6-10度良好，<6度一般
        - Hook密度：<20秒优秀，20-30秒良好，>30秒一般
        - 逆向旋律：使用优秀，部分使用良好，不使用一般

        满分：100分
        """
        score = 0
        details = {}

        # 获取参数，如果没有则使用估算值
        interval_range = data.technical_data.get("melody_structure", {}).get("interval_range", 0)
        hook_density = data.technical_data.get("melody_structure", {}).get("hook_density", 0)
        reverse_melody = data.technical_data.get("melody_structure", {}).get("reverse_melody", False)

        # 如果参数都是0，根据音乐风格进行估算
        if interval_range == 0 and hook_density == 0:
            # 根据音乐类型估算参数
            genre = getattr(data, 'genre', '').lower()
            if '流行' in genre or 'pop' in genre:
                interval_range = 11  # 流行音乐通常有较大音程跨度
                hook_density = 23  # Hook间隔适中
                reverse_melody = False  # 很少使用逆向旋律
            elif '摇滚' in genre or 'rock' in genre:
                interval_range = 13  # 摇滚音程跨度更大
                hook_density = 18  # Hook更密集
                reverse_melody = True  # 可能使用逆向旋律
            elif '民谣' in genre or 'folk' in genre:
                interval_range = 8  # 民谣音程跨度较小
                hook_density = 35  # Hook较稀疏
                reverse_melody = False  # 很少使用逆向旋律
            else:
                # 默认估算（基于大模型示例）
                interval_range = 11
                hook_density = 23
                reverse_melody = False

        # 评分计算（参考大模型评分标准）
        # 1. 音程跨度评分（40分）
        if interval_range >= 10:
            interval_score = 40  # 优秀（对应大模型+10分）
            details["interval_range"] = f"主歌到副歌音程跨度{interval_range}度，符合流行曲动态张力设计，+40分"
        elif interval_range >= 6:
            interval_score = 28  # 良好
            details["interval_range"] = f"主歌到副歌音程跨度{interval_range}度，有一定张力，+28分"
        else:
            interval_score = 15  # 一般
            details["interval_range"] = f"主歌到副歌音程跨度{interval_range}度，张力不足，+15分"

        # 2. Hook密度评分（35分）
        if hook_density > 0 and hook_density <= 15:
            hook_score = 35  # 优秀
            details["hook_density"] = f"Hook重复间隔为{hook_density}秒，符合黄金记忆点标准，+35分"
        elif hook_density <= 25:
            hook_score = 27  # 良好（对应大模型-8分的逻辑）
            details["hook_density"] = f"Hook重复间隔为{hook_density}秒，间隔适中，+27分"
        else:
            hook_score = 15  # 一般
            details["hook_density"] = f"Hook重复间隔为{hook_density}秒，间隔过长，+15分"

        # 3. 逆向旋律发展评分（25分）
        if reverse_melody:
            reverse_score = 25  # 优秀
            details["reverse_melody"] = "采用逆向旋律发展，使用镜像/倒影手法，+25分"
        else:
            reverse_score = 15  # 一般（对应大模型-10分的逻辑）
            details["reverse_melody"] = "主歌旋律线为阶梯上行，副歌未使用逆向发展，缺乏镜像/倒影手法，+15分"

        # 总分计算
        score = interval_score + hook_score + reverse_score

        return {
            "score": score,
            "details": details
        }
