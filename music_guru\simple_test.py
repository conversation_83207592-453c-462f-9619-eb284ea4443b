#!/usr/bin/env python
# -*- coding: utf-8 -*-

print("开始测试...")

try:
    from utils.lyrics_format_manager import LyricsFormatManager
    print("✓ 成功导入 LyricsFormatManager")
    
    manager = LyricsFormatManager()
    print("✓ 成功创建 LyricsFormatManager 实例")
    
    formats = manager.get_supported_formats()
    print(f"✓ 支持的格式: {formats}")
    
except Exception as e:
    print(f"✗ 错误: {e}")
    import traceback
    traceback.print_exc()

print("测试完成")
