#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
SRT字幕格式插件

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import re
from typing import Dict, Optional
from utils.lyrics_format_manager import LyricsFormatPlugin


class SrtLyricsPlugin(LyricsFormatPlugin):
    """SRT字幕格式插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "SRT字幕歌词"
        self.description = "支持SRT字幕格式的歌词文件"
        self.file_extensions = [".srt"]
    
    def can_handle(self, file_path: str) -> bool:
        """检查是否能处理指定文件"""
        ext = os.path.splitext(file_path)[1].lower()
        return ext in self.file_extensions
    
    def read_lyrics(self, file_path: str) -> Optional[str]:
        """读取SRT歌词文件"""
        try:
            # 尝试多种编码
            encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin1']
            
            for encoding in encodings:
                try:
                    with open(file_path, 'r', encoding=encoding) as f:
                        content = f.read().strip()
                        if content:
                            # 解析SRT格式，提取纯歌词
                            lyrics = self._parse_srt_content(content)
                            if lyrics:
                                self.logger.info(f"成功读取SRT歌词文件 ({encoding}): {file_path}")
                                return lyrics
                except UnicodeDecodeError:
                    continue
                except Exception as e:
                    self.logger.warning(f"使用编码 {encoding} 读取失败: {e}")
                    continue
            
            self.logger.error(f"无法读取SRT歌词文件: {file_path}")
            return None
            
        except Exception as e:
            self.logger.error(f"读取SRT歌词文件失败: {e}")
            return None
    
    def _parse_srt_content(self, content: str) -> str:
        """解析SRT内容，提取纯歌词"""
        # SRT格式：
        # 1
        # 00:00:01,000 --> 00:00:04,000
        # 歌词内容
        # 
        # 2
        # 00:00:05,000 --> 00:00:08,000
        # 下一行歌词
        
        blocks = content.split('\n\n')
        lyrics_lines = []
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                # 跳过序号和时间轴，提取歌词
                for line in lines[2:]:
                    line = line.strip()
                    if line:
                        lyrics_lines.append(line)
        
        return '\n'.join(lyrics_lines)
    
    def write_lyrics(self, file_path: str, lyrics: str, metadata: Dict = None) -> bool:
        """写入SRT歌词文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                lines = lyrics.split('\n')
                
                for i, line in enumerate(lines):
                    if line.strip():
                        # 序号
                        f.write(f"{i + 1}\n")
                        
                        # 时间轴（每行间隔4秒）
                        start_time = i * 4
                        end_time = (i + 1) * 4
                        
                        start_h = start_time // 3600
                        start_m = (start_time % 3600) // 60
                        start_s = start_time % 60
                        
                        end_h = end_time // 3600
                        end_m = (end_time % 3600) // 60
                        end_s = end_time % 60
                        
                        f.write(f"{start_h:02d}:{start_m:02d}:{start_s:02d},000 --> ")
                        f.write(f"{end_h:02d}:{end_m:02d}:{end_s:02d},000\n")
                        
                        # 歌词内容
                        f.write(f"{line}\n\n")
            
            self.logger.info(f"成功写入SRT歌词文件: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"写入SRT歌词文件失败: {e}")
            return False
    
    def get_metadata(self, file_path: str) -> Dict:
        """获取SRT歌词文件的元数据"""
        # SRT文件通常不包含元数据，返回基本信息
        metadata = {}
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            # 统计字幕条数
            blocks = content.split('\n\n')
            metadata['subtitle_count'] = len([b for b in blocks if b.strip()])
            
        except Exception as e:
            self.logger.error(f"获取SRT歌词元数据失败: {e}")
            
        return metadata
