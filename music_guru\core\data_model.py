#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
数据模型

MIT License
Copyright (c) 2025 Music Guru
"""

from typing import Dict, List, Any
import json
import os


class MusicData:
    """音乐数据模型"""
    
    def __init__(self):
        """初始化音乐数据"""
        self.title = ""  # 歌曲标题
        self.artist = ""  # 艺术家
        self.album = ""  # 专辑
        self.release_date = ""  # 发行日期
        self.genre = ""  # 流派
        self.file_path = ""  # 音频文件路径
        
        # 技术创作维度数据
        self.technical_data = {
            "melody_structure": {
                "interval_range": 0,  # 音程跨度
                "hook_density": 0,    # 记忆点密度
                "reverse_melody": False  # 逆向旋律发展
            },
            "harmony_progression": {
                "formula_usage": 0,  # 4536公式化使用率
                "key_changes": 0,    # 调式转换频率
                "alt_chord_ratio": 0  # 替代和弦使用率
            },
            "rhythm_design": {
                "beat_accuracy": 0,  # 强拍位移误差率
                "tiktok_segments": 0,  # TikTok可切片段落数
                "drop_energy": 0  # Drop结构能量梯度
            },
            "arrangement_layers": {
                "instrument_separation": 0,  # 乐器声像分离度
                "silence_ratio": 0,  # 留白时长占比
                "electronic_timing": 0  # 电子元素介入时机误差
            }
        }
        
        # 情感表达维度数据
        self.emotional_data = {
            "emotion_delivery": {
                "chorus_range": 0,  # 副歌最高音与最低音差
                "dynamic_range": 0,  # 动态范围DR值
                "synesthesia_count": 0  # 通感修辞数量
            },
            "narrative_tension": {
                "scene_density": 0,  # 具象场景描写密度
                "plot_structure": False,  # 情感转折点符合三幕剧结构
                "lyrics_clarity": 0  # 歌词语义模糊度
            },
            "era_resonance": {
                "trending_words_ratio": 0,  # 年度热词覆盖率
                "gen_z_symbols": 0,  # Z世代文化符号植入数量
                "metaverse_metaphors": False  # 元宇宙相关隐喻
            }
        }
        
        # 市场传播维度数据
        self.market_data = {
            "streaming_adaptation": {
                "intro_hook_timing": 0,  # 前奏吸引力峰值出现时间
                "mobile_optimization": False  # 移动端频响优化通过EBU R128标准
            },
            "chart_performance": {
                "youni_weeks": 0,  # 由你榜TOP10周数
                "wave_score": 0,  # 浪潮榜专业评分
                "overlap_ratio": 0  # 双榜重叠率
            },
            "commercial_conversion": {
                "ost_fee": 0,  # OST授权费
                "concert_conversion": 0,  # 演唱会票房转化率
                "brand_premium": 0  # 品牌联名溢价
            }
        }
        
        # 文化创新维度数据
        self.cultural_data = {
            "style_fusion": {
                "cross_genre_ratio": 0,  # 跨流派元素占比
                "subculture_density": 0,  # 亚文化符号密度
                "ai_content_originality": False  # AI生成内容原创性认证
            },
            "concept_breakthrough": {
                "visual_system": False,  # 构建专属视觉符号系统
                "album_coherence": 0,  # 专辑叙事连贯性
                "moral_boundary": 0  # 挑战道德审查边界
            }
        }
        
        # 制作工程维度数据
        self.production_data = {
            "recording_quality": {
                "dynamic_range": 0,  # 动态范围
                "noise_floor": 0,  # 底噪电平
                "apple_masters": False  # 符合Apple Digital Masters认证
            },
            "mixing_mastering": {
                "lufs_standard": 0,  # 响度标准化
                "spatial_accuracy": 0,  # 空间定位误差
                "car_audio_test": False  # 车载音响兼容性测试通过
            },
            "vocal_processing": {
                "autotune_artistry": 0,  # Auto-Tune使用艺术性分级
                "harmony_layers": 0  # 和声堆叠层数
            }
        }
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        return {
            "title": self.title,
            "artist": self.artist,
            "album": self.album,
            "release_date": self.release_date,
            "genre": self.genre,
            "file_path": self.file_path,
            "technical_data": self.technical_data,
            "emotional_data": self.emotional_data,
            "market_data": self.market_data,
            "cultural_data": self.cultural_data,
            "production_data": self.production_data
        }
    
    def from_dict(self, data: Dict):
        """从字典加载数据"""
        self.title = data.get("title", "")
        self.artist = data.get("artist", "")
        self.album = data.get("album", "")
        self.release_date = data.get("release_date", "")
        self.genre = data.get("genre", "")
        self.file_path = data.get("file_path", "")
        
        # 加载各维度数据
        if "technical_data" in data:
            self.technical_data.update(data["technical_data"])
        
        if "emotional_data" in data:
            self.emotional_data.update(data["emotional_data"])
        
        if "market_data" in data:
            self.market_data.update(data["market_data"])
        
        if "cultural_data" in data:
            self.cultural_data.update(data["cultural_data"])
        
        if "production_data" in data:
            self.production_data.update(data["production_data"])
    
    def save_to_file(self, file_path: str):
        """保存到文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.to_dict(), f, indent=4, ensure_ascii=False)
            return True
        except Exception as e:
            print(f"保存数据失败: {e}")
            return False
    
    def load_from_file(self, file_path: str):
        """从文件加载"""
        try:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.from_dict(data)
                return True
        except Exception as e:
            print(f"加载数据失败: {e}")
        return False
