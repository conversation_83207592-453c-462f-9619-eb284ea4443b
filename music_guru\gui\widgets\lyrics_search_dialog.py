#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
歌词搜索对话框

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import requests
import json
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QLineEdit, QTextEdit, QTabWidget,
                            QWidget, QListWidget, QListWidgetItem, QCheckBox,
                            QComboBox, QProgressBar, QMessageBox, QSplitter)
from PyQt6.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtGui import QFont


class LyricsSearchThread(QThread):
    """歌词搜索线程"""
    
    search_finished = pyqtSignal(list)  # 搜索结果
    search_error = pyqtSignal(str)  # 错误信息
    
    def __init__(self, artist, title):
        super().__init__()
        self.artist = artist
        self.title = title
    
    def run(self):
        """执行搜索"""
        try:
            results = self.search_lyrics()
            self.search_finished.emit(results)
        except Exception as e:
            self.search_error.emit(str(e))
    
    def search_lyrics(self):
        """搜索歌词"""
        results = []
        
        # 搜索源1: 网易云音乐API（示例）
        try:
            netease_results = self.search_netease()
            results.extend(netease_results)
        except Exception as e:
            print(f"网易云搜索失败: {e}")
        
        # 搜索源2: QQ音乐API（示例）
        try:
            qq_results = self.search_qq_music()
            results.extend(qq_results)
        except Exception as e:
            print(f"QQ音乐搜索失败: {e}")
        
        # 搜索源3: 歌词网站爬虫（示例）
        try:
            web_results = self.search_lyrics_websites()
            results.extend(web_results)
        except Exception as e:
            print(f"歌词网站搜索失败: {e}")
        
        return results
    
    def search_netease(self):
        """搜索网易云音乐（示例实现）"""
        # 注意：这只是示例代码，实际使用需要遵守相关API的使用条款
        results = []
        
        # 模拟搜索结果
        if self.artist and self.title:
            results.append({
                "source": "网易云音乐",
                "title": self.title,
                "artist": self.artist,
                "lyrics": f"这是来自网易云音乐的 {self.title} - {self.artist} 的歌词内容...\n\n（这是示例内容，实际需要调用真实API）",
                "url": "https://music.163.com"
            })
        
        return results
    
    def search_qq_music(self):
        """搜索QQ音乐（示例实现）"""
        results = []
        
        # 模拟搜索结果
        if self.artist and self.title:
            results.append({
                "source": "QQ音乐",
                "title": self.title,
                "artist": self.artist,
                "lyrics": f"这是来自QQ音乐的 {self.title} - {self.artist} 的歌词内容...\n\n（这是示例内容，实际需要调用真实API）",
                "url": "https://y.qq.com"
            })
        
        return results
    
    def search_lyrics_websites(self):
        """搜索歌词网站（示例实现）"""
        results = []
        
        # 模拟搜索结果
        if self.artist and self.title:
            results.append({
                "source": "歌词网站",
                "title": self.title,
                "artist": self.artist,
                "lyrics": f"这是来自歌词网站的 {self.title} - {self.artist} 的歌词内容...\n\n（这是示例内容，实际需要实现网页爬虫）",
                "url": "https://www.example-lyrics.com"
            })
        
        return results


class LyricsSearchDialog(QDialog):
    """歌词搜索对话框"""
    
    lyrics_selected = pyqtSignal(str, dict)  # 选中的歌词和元数据
    
    def __init__(self, parent=None, artist="", title=""):
        super().__init__(parent)
        
        self.setWindowTitle("搜索歌词和曲谱")
        self.setMinimumSize(1000, 700)
        
        self.artist = artist
        self.title = title
        self.search_results = []
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 搜索区域
        search_layout = QHBoxLayout()
        
        search_layout.addWidget(QLabel("艺术家:"))
        self.artist_edit = QLineEdit(self.artist)
        search_layout.addWidget(self.artist_edit)
        
        search_layout.addWidget(QLabel("歌曲名:"))
        self.title_edit = QLineEdit(self.title)
        search_layout.addWidget(self.title_edit)
        
        self.search_button = QPushButton("搜索")
        self.search_button.clicked.connect(self.start_search)
        search_layout.addWidget(self.search_button)
        
        layout.addLayout(search_layout)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 主要内容区域
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        layout.addWidget(main_splitter)
        
        # 左侧：搜索结果列表
        left_widget = QWidget()
        left_layout = QVBoxLayout(left_widget)
        
        left_layout.addWidget(QLabel("搜索结果:"))
        self.results_list = QListWidget()
        self.results_list.itemClicked.connect(self.on_result_selected)
        left_layout.addWidget(self.results_list)
        
        main_splitter.addWidget(left_widget)
        
        # 右侧：选项卡
        self.tab_widget = QTabWidget()
        main_splitter.addWidget(self.tab_widget)
        
        # 歌词预览选项卡
        self.lyrics_preview = QTextEdit()
        self.lyrics_preview.setReadOnly(True)
        self.lyrics_preview.setFont(QFont("Microsoft YaHei", 10))
        self.tab_widget.addTab(self.lyrics_preview, "歌词预览")
        
        # 网页搜索选项卡
        try:
            self.web_view = QWebEngineView()
            self.tab_widget.addTab(self.web_view, "网页搜索")
            
            # 添加网页搜索控制
            web_control_layout = QHBoxLayout()
            
            self.url_edit = QLineEdit()
            self.url_edit.setPlaceholderText("输入搜索URL或使用预设搜索引擎")
            web_control_layout.addWidget(self.url_edit)
            
            self.search_engine_combo = QComboBox()
            self.search_engine_combo.addItems([
                "百度搜索歌词",
                "Google搜索歌词", 
                "歌词网站1",
                "歌词网站2"
            ])
            self.search_engine_combo.currentTextChanged.connect(self.on_search_engine_changed)
            web_control_layout.addWidget(self.search_engine_combo)
            
            web_search_button = QPushButton("搜索")
            web_search_button.clicked.connect(self.search_in_web)
            web_control_layout.addWidget(web_search_button)
            
            # 将控制栏添加到网页选项卡的顶部
            web_widget = QWidget()
            web_layout = QVBoxLayout(web_widget)
            web_layout.addLayout(web_control_layout)
            web_layout.addWidget(self.web_view)
            
            # 替换原来的web_view选项卡
            self.tab_widget.removeTab(1)
            self.tab_widget.addTab(web_widget, "网页搜索")
            
        except ImportError:
            # 如果没有安装QtWebEngine，显示提示
            web_placeholder = QLabel("网页搜索功能需要安装 PyQt6-WebEngine")
            web_placeholder.setAlignment(Qt.AlignmentFlag.AlignCenter)
            self.tab_widget.addTab(web_placeholder, "网页搜索")
        
        # 设置分割器比例
        main_splitter.setSizes([300, 700])
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        # 保存选项
        save_options_layout = QHBoxLayout()
        
        self.save_lyrics_checkbox = QCheckBox("保存歌词")
        self.save_lyrics_checkbox.setChecked(True)
        save_options_layout.addWidget(self.save_lyrics_checkbox)
        
        self.save_format_combo = QComboBox()
        self.save_format_combo.addItems(["TXT", "LRC", "JSON"])
        save_options_layout.addWidget(self.save_format_combo)
        
        button_layout.addLayout(save_options_layout)
        
        button_layout.addStretch()
        
        self.use_button = QPushButton("使用选中歌词")
        self.use_button.clicked.connect(self.use_selected_lyrics)
        self.use_button.setEnabled(False)
        button_layout.addWidget(self.use_button)
        
        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(cancel_button)
        
        layout.addLayout(button_layout)
    
    def start_search(self):
        """开始搜索"""
        artist = self.artist_edit.text().strip()
        title = self.title_edit.text().strip()
        
        if not artist or not title:
            QMessageBox.warning(self, "警告", "请输入艺术家和歌曲名")
            return
        
        self.search_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条
        self.results_list.clear()
        
        # 启动搜索线程
        self.search_thread = LyricsSearchThread(artist, title)
        self.search_thread.search_finished.connect(self.on_search_finished)
        self.search_thread.search_error.connect(self.on_search_error)
        self.search_thread.start()
    
    def on_search_finished(self, results):
        """搜索完成"""
        self.search_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        
        self.search_results = results
        
        for result in results:
            item_text = f"{result['source']} - {result['title']} ({result['artist']})"
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, result)
            self.results_list.addItem(item)
        
        if not results:
            QMessageBox.information(self, "搜索结果", "未找到相关歌词")
    
    def on_search_error(self, error):
        """搜索错误"""
        self.search_button.setEnabled(True)
        self.progress_bar.setVisible(False)
        QMessageBox.critical(self, "搜索错误", f"搜索失败: {error}")
    
    def on_result_selected(self, item):
        """选择搜索结果"""
        result = item.data(Qt.ItemDataRole.UserRole)
        if result:
            self.lyrics_preview.setText(result['lyrics'])
            self.use_button.setEnabled(True)
    
    def on_search_engine_changed(self, engine_name):
        """搜索引擎改变"""
        artist = self.artist_edit.text().strip()
        title = self.title_edit.text().strip()
        
        if not artist or not title:
            return
        
        search_query = f"{artist} {title} 歌词"
        
        if "百度" in engine_name:
            url = f"https://www.baidu.com/s?wd={search_query}"
        elif "Google" in engine_name:
            url = f"https://www.google.com/search?q={search_query}"
        else:
            url = f"https://www.baidu.com/s?wd={search_query}"
        
        self.url_edit.setText(url)
    
    def search_in_web(self):
        """在网页中搜索"""
        url = self.url_edit.text().strip()
        if not url:
            artist = self.artist_edit.text().strip()
            title = self.title_edit.text().strip()
            if artist and title:
                search_query = f"{artist} {title} 歌词"
                url = f"https://www.baidu.com/s?wd={search_query}"
        
        if url and hasattr(self, 'web_view'):
            if not url.startswith(('http://', 'https://')):
                url = 'https://' + url
            self.web_view.load(url)
    
    def use_selected_lyrics(self):
        """使用选中的歌词"""
        current_item = self.results_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择一个搜索结果")
            return
        
        result = current_item.data(Qt.ItemDataRole.UserRole)
        lyrics = result['lyrics']
        
        # 准备元数据
        metadata = {
            "title": result['title'],
            "artist": result['artist'],
            "source": result['source'],
            "url": result.get('url', ''),
            "save_lyrics": self.save_lyrics_checkbox.isChecked(),
            "save_format": self.save_format_combo.currentText().lower()
        }
        
        self.lyrics_selected.emit(lyrics, metadata)
        self.accept()
