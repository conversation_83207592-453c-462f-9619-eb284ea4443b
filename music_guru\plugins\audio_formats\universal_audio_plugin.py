#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
通用音频格式插件 - 支持各种音频格式

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import io
import struct
import logging
from typing import Tuple, Optional, Dict, Any, List

from utils.audio_format_plugin import AudioFormatPlugin

# 尝试导入各种音频处理库
backends = {}

try:
    import librosa
    backends["librosa"] = True
except ImportError:
    backends["librosa"] = False

try:
    import soundfile
    backends["soundfile"] = True
except ImportError:
    backends["soundfile"] = False

try:
    import pydub
    backends["pydub"] = True
except ImportError:
    backends["pydub"] = False

try:
    import miniaudio
    backends["miniaudio"] = True
except ImportError:
    backends["miniaudio"] = False

try:
    import tinytag
    backends["tinytag"] = True
except ImportError:
    backends["tinytag"] = False

try:
    import mutagen
    backends["mutagen"] = True
except ImportError:
    backends["mutagen"] = False

try:
    import wave
    backends["wave"] = True
except ImportError:
    backends["wave"] = False

try:
    import audioread
    backends["audioread"] = True
except ImportError:
    backends["audioread"] = False


class UniversalAudioPlugin(AudioFormatPlugin):
    """通用音频格式插件 - 支持各种音频格式"""
    
    def __init__(self):
        """初始化通用音频格式插件"""
        super().__init__()
        self.name = "通用音频格式"
        self.description = "支持各种音频格式，包括WAV, AIFF, OGG, AAC, M4A, WMA等"
        self.extensions = [
            "wav", "wave", "aif", "aiff", "ogg", "oga", "mogg", 
            "aac", "m4a", "m4b", "mp4", "3gp", "wma", "au", "snd",
            "voc", "ape", "mpc", "opus", "wv", "spx", "ra", "rm",
            "dts", "dtshd", "ac3", "eac3", "caf", "amr", "mid", "midi"
        ]
        self.mime_types = [
            "audio/wav", "audio/x-wav", "audio/wave", "audio/x-pn-wav",
            "audio/aiff", "audio/x-aiff",
            "audio/ogg", "application/ogg", "audio/vorbis", "audio/vorbis-config",
            "audio/aac", "audio/aacp", "audio/3gpp", "audio/3gpp2",
            "audio/mp4", "audio/mp4a-latm", "audio/mpeg4-generic",
            "audio/x-m4a", "audio/x-m4b",
            "audio/x-ms-wma", "audio/x-ms-wax",
            "audio/basic",
            "audio/voc",
            "audio/x-ape", "audio/x-musepack",
            "audio/opus",
            "audio/x-wavpack",
            "audio/x-speex",
            "audio/x-pn-realaudio", "audio/x-realaudio",
            "audio/vnd.dts", "audio/vnd.dts.hd",
            "audio/ac3", "audio/eac3",
            "audio/x-caf",
            "audio/amr", "audio/amr-wb",
            "audio/midi", "audio/mid"
        ]
        self.priority = 10  # 低优先级，作为后备选项
    
    def _load_backends(self) -> Dict[str, bool]:
        """加载后端库"""
        return backends
    
    def can_handle(self, file_path: str) -> bool:
        """
        检查是否可以处理指定文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            bool: 是否可以处理
        """
        if not os.path.exists(file_path):
            return False
        
        ext = self.get_file_extension(file_path)
        if ext in self.extensions:
            # 对于已知扩展名，直接返回True
            return True
        
        # 尝试使用多种方法检测音频格式
        try:
            # 方法1: 使用librosa
            if backends["librosa"]:
                try:
                    librosa.load(file_path, sr=None, mono=True, offset=0.0, duration=0.1)
                    return True
                except:
                    pass
            
            # 方法2: 使用soundfile
            if backends["soundfile"]:
                try:
                    soundfile.info(file_path)
                    return True
                except:
                    pass
            
            # 方法3: 使用pydub
            if backends["pydub"]:
                try:
                    pydub.AudioSegment.from_file(file_path)
                    return True
                except:
                    pass
            
            # 方法4: 使用miniaudio
            if backends["miniaudio"]:
                try:
                    miniaudio.decode_file(file_path)
                    return True
                except:
                    pass
            
            # 方法5: 使用tinytag
            if backends["tinytag"]:
                try:
                    tag = tinytag.TinyTag.get(file_path)
                    if tag.duration > 0:
                        return True
                except:
                    pass
            
            # 方法6: 使用mutagen
            if backends["mutagen"]:
                try:
                    audio = mutagen.File(file_path)
                    if audio and hasattr(audio, 'info') and hasattr(audio.info, 'length'):
                        return True
                except:
                    pass
            
            # 方法7: 使用audioread
            if backends["audioread"]:
                try:
                    with audioread.audio_open(file_path) as f:
                        if f.duration > 0:
                            return True
                except:
                    pass
            
            return False
        except Exception as e:
            self.logger.error(f"检查音频文件失败: {e}")
            return False
    
    def get_version(self, file_path: str) -> str:
        """
        获取音频文件版本
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件版本
        """
        ext = self.get_file_extension(file_path)
        
        # 对于WAV文件，尝试获取格式信息
        if ext == "wav" and backends["wave"]:
            try:
                with wave.open(file_path, 'rb') as wav_file:
                    channels = wav_file.getnchannels()
                    sample_width = wav_file.getsampwidth()
                    frame_rate = wav_file.getframerate()
                    compression_type = wav_file.getcomptype()
                    
                    return f"WAV {compression_type} {sample_width*8}bit {frame_rate}Hz {channels}ch"
            except:
                pass
        
        # 对于其他格式，使用tinytag获取基本信息
        if backends["tinytag"]:
            try:
                tag = tinytag.TinyTag.get(file_path)
                if tag.samplerate and tag.channels:
                    return f"{ext.upper()} {int(tag.bitrate)}kbps {int(tag.samplerate)}Hz {tag.channels}ch"
            except:
                pass
        
        # 使用mutagen获取格式信息
        if backends["mutagen"]:
            try:
                audio = mutagen.File(file_path)
                if audio and hasattr(audio, 'info'):
                    info = audio.info
                    details = []
                    
                    if hasattr(info, 'bitrate') and info.bitrate:
                        details.append(f"{int(info.bitrate/1000)}kbps")
                    
                    if hasattr(info, 'sample_rate') and info.sample_rate:
                        details.append(f"{int(info.sample_rate)}Hz")
                    
                    if hasattr(info, 'channels') and info.channels:
                        details.append(f"{info.channels}ch")
                    
                    if details:
                        return f"{ext.upper()} {' '.join(details)}"
            except:
                pass
        
        return f"{ext.upper()} format"
    
    def load_audio(self, file_path: str) -> Tuple[Optional[Any], Optional[int]]:
        """
        加载音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Tuple[Optional[Any], Optional[int]]: 音频数据和采样率
        """
        # 尝试使用多种方法加载音频
        errors = []
        
        # 方法1: 使用librosa
        if backends["librosa"]:
            try:
                audio_data, sample_rate = librosa.load(file_path, sr=None)
                return audio_data, sample_rate
            except Exception as e:
                errors.append(f"librosa加载失败: {e}")
        
        # 方法2: 使用soundfile
        if backends["soundfile"]:
            try:
                audio_data, sample_rate = soundfile.read(file_path)
                return audio_data, sample_rate
            except Exception as e:
                errors.append(f"soundfile加载失败: {e}")
        
        # 方法3: 使用pydub
        if backends["pydub"]:
            try:
                audio = pydub.AudioSegment.from_file(file_path)
                sample_rate = audio.frame_rate
                # 转换为numpy数组
                import numpy as np
                audio_data = np.array(audio.get_array_of_samples()) / 32768.0  # 归一化到[-1, 1]
                if audio.channels == 2:
                    audio_data = audio_data.reshape((-1, 2))
                return audio_data, sample_rate
            except Exception as e:
                errors.append(f"pydub加载失败: {e}")
        
        # 方法4: 使用miniaudio
        if backends["miniaudio"]:
            try:
                audio = miniaudio.decode_file(file_path)
                import numpy as np
                audio_data = np.frombuffer(audio.samples, dtype=np.float32)
                if audio.nchannels == 2:
                    audio_data = audio_data.reshape((-1, 2))
                return audio_data, audio.sample_rate
            except Exception as e:
                errors.append(f"miniaudio加载失败: {e}")
        
        # 记录所有错误
        if errors:
            self.logger.error(f"加载音频文件失败: {'; '.join(errors)}")
        
        return None, None
    
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        获取音频元数据
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Dict[str, Any]: 元数据字典
        """
        metadata = {
            "title": "",
            "artist": "",
            "album": "",
            "year": "",
            "genre": "",
            "duration": 0,
            "bitrate": 0,
            "sample_rate": 0,
            "channels": 0
        }
        
        errors = []
        
        # 方法1: 使用tinytag
        if backends["tinytag"]:
            try:
                tag = tinytag.TinyTag.get(file_path)
                metadata["title"] = tag.title or ""
                metadata["artist"] = tag.artist or ""
                metadata["album"] = tag.album or ""
                metadata["year"] = tag.year or ""
                metadata["genre"] = tag.genre or ""
                metadata["duration"] = tag.duration
                metadata["bitrate"] = tag.bitrate
                metadata["sample_rate"] = tag.samplerate
                metadata["channels"] = tag.channels
                
                if any([metadata["title"], metadata["artist"], metadata["album"]]):
                    return metadata
            except Exception as e:
                errors.append(f"tinytag获取元数据失败: {e}")
        
        # 方法2: 使用mutagen
        if backends["mutagen"]:
            try:
                audio = mutagen.File(file_path)
                if audio:
                    # 尝试获取常见标签
                    if "title" in audio:
                        metadata["title"] = str(audio["title"][0])
                    elif "\xa9nam" in audio:  # MP4标题
                        metadata["title"] = str(audio["\xa9nam"][0])
                    
                    if "artist" in audio:
                        metadata["artist"] = str(audio["artist"][0])
                    elif "\xa9ART" in audio:  # MP4艺术家
                        metadata["artist"] = str(audio["\xa9ART"][0])
                    
                    if "album" in audio:
                        metadata["album"] = str(audio["album"][0])
                    elif "\xa9alb" in audio:  # MP4专辑
                        metadata["album"] = str(audio["\xa9alb"][0])
                    
                    if "date" in audio:
                        metadata["year"] = str(audio["date"][0])
                    elif "\xa9day" in audio:  # MP4日期
                        metadata["year"] = str(audio["\xa9day"][0])
                    
                    if "genre" in audio:
                        metadata["genre"] = str(audio["genre"][0])
                    elif "\xa9gen" in audio:  # MP4流派
                        metadata["genre"] = str(audio["\xa9gen"][0])
                    
                    # 获取技术信息
                    if hasattr(audio, 'info'):
                        if hasattr(audio.info, 'length'):
                            metadata["duration"] = audio.info.length
                        
                        if hasattr(audio.info, 'bitrate'):
                            metadata["bitrate"] = audio.info.bitrate
                        
                        if hasattr(audio.info, 'sample_rate'):
                            metadata["sample_rate"] = audio.info.sample_rate
                        
                        if hasattr(audio.info, 'channels'):
                            metadata["channels"] = audio.info.channels
                    
                    if any([metadata["title"], metadata["artist"], metadata["album"]]):
                        return metadata
            except Exception as e:
                errors.append(f"mutagen获取元数据失败: {e}")
        
        # 方法3: 使用soundfile获取技术信息
        if backends["soundfile"]:
            try:
                info = soundfile.info(file_path)
                metadata["duration"] = info.duration
                metadata["sample_rate"] = info.samplerate
                metadata["channels"] = info.channels
                
                # 计算比特率
                if info.duration > 0:
                    file_size = os.path.getsize(file_path)
                    metadata["bitrate"] = int((file_size * 8) / info.duration)
            except Exception as e:
                errors.append(f"soundfile获取元数据失败: {e}")
        
        # 记录所有错误
        if errors:
            self.logger.error(f"获取音频元数据失败: {'; '.join(errors)}")
        
        # 如果所有方法都失败，尝试从文件名获取标题
        if not metadata["title"]:
            metadata["title"] = os.path.basename(file_path).split('.')[0]
        
        return metadata
    
    def get_lyrics(self, file_path: str) -> Optional[str]:
        """
        获取歌词
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Optional[str]: 歌词文本
        """
        errors = []
        
        # 方法1: 使用mutagen尝试获取歌词
        if backends["mutagen"]:
            try:
                audio = mutagen.File(file_path)
                if audio:
                    # 检查常见的歌词标签
                    if "lyrics" in audio:
                        return str(audio["lyrics"][0])
                    
                    if "unsyncedlyrics" in audio:
                        return str(audio["unsyncedlyrics"][0])
                    
                    # MP4歌词
                    if "\xa9lyr" in audio:
                        return str(audio["\xa9lyr"][0])
            except Exception as e:
                errors.append(f"mutagen获取歌词失败: {e}")
        
        # 方法2: 尝试查找同名LRC文件
        try:
            lrc_path = os.path.splitext(file_path)[0] + ".lrc"
            if os.path.exists(lrc_path):
                with open(lrc_path, 'r', encoding='utf-8') as f:
                    return f.read()
        except Exception as e:
            errors.append(f"读取LRC文件失败: {e}")
        
        # 方法3: 尝试查找同名TXT文件
        try:
            txt_path = os.path.splitext(file_path)[0] + ".txt"
            if os.path.exists(txt_path):
                with open(txt_path, 'r', encoding='utf-8') as f:
                    return f.read()
        except Exception as e:
            errors.append(f"读取TXT文件失败: {e}")
        
        # 记录所有错误
        if errors:
            self.logger.debug(f"获取音频歌词失败: {'; '.join(errors)}")
        
        return None
