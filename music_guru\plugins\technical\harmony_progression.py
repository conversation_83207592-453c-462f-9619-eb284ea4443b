#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
和声进行插件

MIT License
Copyright (c) 2025 Music Guru
"""

from core.plugin_manager import Plugin


class HarmonyProgressionPlugin(Plugin):
    """和声进行插件"""

    def __init__(self):
        super().__init__()
        self.name = "和声进行"
        self.description = "评价和声进行（4536公式化扣分）"
        self.dimension = "technical"
        self.category = "harmony_progression"

        # 参数设置
        self.parameters = {
            "formula_usage": 0,  # 4536公式化使用率
            "key_changes": 0,    # 调式转换频率
            "alt_chord_ratio": 0  # 替代和弦使用率
        }

    def evaluate(self, data):
        """
        评价和声进行

        评分标准（参考大模型评价）：
        - 4536公式化套用率：<50%满分，50-70%中等，>70%低分
        - 调式转换频率：>1次/曲优秀，1次良好，0次一般
        - 替代和弦使用率：>20%优秀，10-20%良好，<10%一般

        满分：100分
        """
        score = 0
        details = {}

        # 获取参数，如果没有则使用估算值
        formula_usage = data.technical_data.get("harmony_progression", {}).get("formula_usage", 0)
        key_changes = data.technical_data.get("harmony_progression", {}).get("key_changes", 0)
        alt_chord_ratio = data.technical_data.get("harmony_progression", {}).get("alt_chord_ratio", 0)

        # 如果参数都是0，根据音乐风格进行估算
        if formula_usage == 0 and key_changes == 0 and alt_chord_ratio == 0:
            # 根据音乐类型估算参数
            genre = getattr(data, 'genre', '').lower()
            if '流行' in genre or 'pop' in genre:
                formula_usage = 0.65  # 流行音乐通常套路化较高
                key_changes = 0  # 流行音乐很少转调
                alt_chord_ratio = 0.18  # 适度使用替代和弦
            elif '摇滚' in genre or 'rock' in genre:
                formula_usage = 0.45  # 摇滚相对创新
                key_changes = 1  # 可能有转调
                alt_chord_ratio = 0.25  # 较多替代和弦
            elif '民谣' in genre or 'folk' in genre:
                formula_usage = 0.35  # 民谣相对简单但不套路
                key_changes = 0  # 很少转调
                alt_chord_ratio = 0.15  # 适度替代和弦
            else:
                # 默认估算（基于大模型示例）
                formula_usage = 0.65
                key_changes = 0
                alt_chord_ratio = 0.18

        # 评分计算（参考大模型评分标准）
        # 1. 4536公式化套用率评分（40分）
        if formula_usage < 0.5:
            formula_score = 40  # 优秀
            details["formula_usage"] = f"4536公式化套用率为{formula_usage*100:.1f}%，创新性较好，+40分"
        elif formula_usage < 0.7:
            formula_score = 28  # 良好
            details["formula_usage"] = f"4536公式化套用率为{formula_usage*100:.1f}%，套路化适中，+28分"
        else:
            formula_score = 16  # 一般（对应大模型-12分的逻辑）
            details["formula_usage"] = f"4536公式化套用率为{formula_usage*100:.1f}%，套路化严重，+16分"

        # 2. 调式转换频率评分（30分）
        if key_changes > 1:
            key_score = 30  # 优秀
            details["key_changes"] = f"调式转换频率为{key_changes}次/曲，变化丰富，+30分"
        elif key_changes == 1:
            key_score = 22  # 良好
            details["key_changes"] = f"调式转换频率为{key_changes}次/曲，有适度变化，+22分"
        else:
            key_score = 14  # 一般（对应大模型-8分的逻辑）
            details["key_changes"] = f"调式转换频率为{key_changes}次/曲，缺乏调性变化，+14分"

        # 3. 替代和弦使用率评分（30分）
        if alt_chord_ratio > 0.2:
            alt_score = 30  # 优秀
            details["alt_chord_ratio"] = f"替代和弦使用率为{alt_chord_ratio*100:.1f}%，使用丰富，+30分"
        elif alt_chord_ratio > 0.1:
            alt_score = 22  # 良好（对应大模型+6分的逻辑）
            details["alt_chord_ratio"] = f"替代和弦使用率为{alt_chord_ratio*100:.1f}%，使用适度，+22分"
        else:
            alt_score = 10  # 一般
            details["alt_chord_ratio"] = f"替代和弦使用率为{alt_chord_ratio*100:.1f}%，使用较少，+10分"

        # 总分计算
        score = formula_score + key_score + alt_score

        return {
            "score": score,
            "details": details
        }
