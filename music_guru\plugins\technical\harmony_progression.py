#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
和声进行插件

MIT License
Copyright (c) 2025 Music Guru
"""

from core.plugin_manager import Plugin


class HarmonyProgressionPlugin(Plugin):
    """和声进行插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "和声进行"
        self.description = "评价和声进行（4536公式化扣分）"
        self.dimension = "technical"
        self.category = "harmony_progression"
        
        # 参数设置
        self.parameters = {
            "formula_usage": 0,  # 4536公式化使用率
            "key_changes": 0,    # 调式转换频率
            "alt_chord_ratio": 0  # 替代和弦使用率
        }
    
    def evaluate(self, data):
        """
        评价和声进行
        
        评分标准：
        - 4536公式化套用（-3分）
        - 调式转换频率＞3次/曲（+2分）
        - 替代和弦使用率＞25%（+4分）
        
        满分：6分
        """
        score = 0
        details = {}
        
        # 获取参数
        formula_usage = data.technical_data.get("harmony_progression", {}).get("formula_usage", 0)
        key_changes = data.technical_data.get("harmony_progression", {}).get("key_changes", 0)
        alt_chord_ratio = data.technical_data.get("harmony_progression", {}).get("alt_chord_ratio", 0)
        
        # 评分计算
        # 1. 4536公式化套用（-3分）
        if formula_usage > 0.7:  # 如果公式化使用率超过70%
            score -= 3
            details["formula_usage"] = f"4536公式化套用率为{formula_usage*100:.1f}%，超过70%，-3分"
        else:
            details["formula_usage"] = f"4536公式化套用率为{formula_usage*100:.1f}%，未扣分"
        
        # 2. 调式转换频率＞3次/曲（+2分）
        if key_changes > 3:
            score += 2
            details["key_changes"] = f"调式转换频率为{key_changes}次/曲，大于3次，+2分"
        else:
            details["key_changes"] = f"调式转换频率为{key_changes}次/曲，不符合要求，+0分"
        
        # 3. 替代和弦使用率＞25%（+4分）
        if alt_chord_ratio > 0.25:
            score += 4
            details["alt_chord_ratio"] = f"替代和弦使用率为{alt_chord_ratio*100:.1f}%，大于25%，+4分"
        else:
            details["alt_chord_ratio"] = f"替代和弦使用率为{alt_chord_ratio*100:.1f}%，不符合要求，+0分"
        
        # 标准化分数到100分制
        # 基础分为3分（避免负分）
        base_score = 3
        normalized_score = ((score + base_score) / 9) * 100
        normalized_score = max(0, min(normalized_score, 100))  # 确保分数在0-100之间
        
        return {
            "score": normalized_score,
            "details": details
        }
