# Music Guru - 流行音乐评价软件

Music Guru 是一款专业的流行音乐评价软件，基于多维度参数对音乐作品进行全面评价。软件采用插件式架构，便于后期扩展，支持Docker部署。

## 功能特点

- **多维度评价**：从技术创作、情感表达、市场传播、文化创新、制作工程五个核心维度进行评价
- **参数化评分**：每个维度包含多个具体参数，可单独评分
- **插件式架构**：所有评价参数和评价方式均采用插件形式，便于扩展
- **可视化界面**：直观的GUI界面，支持参数勾选和结果展示
- **加权评分**：根据不同维度的权重计算综合评分
- **动态校准**：支持根据时代特征和流派特点进行动态校准

## 评价维度

### 1. 技术创作维度（权重30%）

- **旋律结构**：音程跨度、记忆点密度
- **和声进行**：4536公式化扣分、调式转换频率、替代和弦使用率
- **节奏设计**：律动适配性、TikTok切片潜力
- **编曲层次**：乐器融合度、空间留白

### 2. 情感表达维度（权重25%）

- **情绪传达**：副歌冲击力、动态对比
- **叙事张力**：歌词细节真实度、戏剧转折
- **时代共鸣**：社交媒体热词覆盖率

### 3. 市场传播维度（权重20%）

- **流媒体适配**：前5秒留存率
- **榜单成绩**：由你榜/浪潮榜双维度
- **商业转化**：OST价值、演唱会票房

### 4. 文化创新维度（权重15%）

- **风格融合**：跨流派元素占比
- **概念突破**：亚文化符号密度
- **技术实验**：AI生成内容占比

### 5. 制作工程维度（权重10%）

- **录音质量**：动态范围、LUFS标准
- **混音母带**：移动端频响优化
- **人声处理**：Auto-Tune艺术性

## 安装与使用

### 依赖项

- Python 3.8+
- PyQt6
- librosa (音频分析)
- numpy

### 安装步骤

1. 克隆仓库

```bash
git clone https://github.com/yourusername/music_guru.git
cd music_guru
```

2. 安装依赖

```bash
pip install -r requirements.txt
```

3. 运行程序

```bash
python main.py
```

### Docker部署

1. 构建Docker镜像

```bash
docker build -t music_guru .
```

2. 运行Docker容器

```bash
docker run -p 8080:8080 music_guru
```

## 使用方法

1. 启动程序后，在左侧面板选择要评价的参数
2. 点击"加载音乐"按钮，选择要评价的音乐文件
3. 点击"开始评价"按钮，系统将根据选择的参数进行评价
4. 评价结果将显示在右侧面板，包括总分、各维度分数和详细结果
5. 可以点击"保存结果"按钮，将评价结果保存为JSON文件

## 插件开发

Music Guru 采用插件式架构，可以方便地扩展新的评价参数和评价方法。

### 创建新插件

1. 在对应维度的插件目录下创建新的Python文件
2. 继承 `Plugin` 基类并实现 `evaluate` 方法
3. 在 `__init__` 方法中设置插件名称、描述、维度和参数

示例：

```python
from core.plugin_manager import Plugin

class MyNewPlugin(Plugin):
    def __init__(self):
        super().__init__()
        self.name = "我的新插件"
        self.description = "这是一个示例插件"
        self.dimension = "technical"  # 所属维度
        self.category = "my_category"
        
        # 参数设置
        self.parameters = {
            "param1": 0,
            "param2": False
        }
    
    def evaluate(self, data):
        # 实现评价逻辑
        score = 0
        details = {}
        
        # 计算分数...
        
        return {
            "score": score,
            "details": details
        }
```

## 许可证

本项目采用 MIT 许可证。详见 [LICENSE](LICENSE) 文件。

## 贡献

欢迎贡献代码、报告问题或提出建议。请通过 GitHub Issues 或 Pull Requests 参与项目开发。
