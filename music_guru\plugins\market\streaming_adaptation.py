#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
流媒体适配插件

MIT License
Copyright (c) 2025 Music Guru
"""

from core.plugin_manager import Plugin


class StreamingAdaptationPlugin(Plugin):
    """流媒体适配插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "流媒体适配"
        self.description = "评价流媒体适配（前5秒留存率）"
        self.dimension = "market"
        self.category = "streaming_adaptation"
        
        # 参数设置
        self.parameters = {
            "intro_hook_timing": 0,  # 前奏吸引力峰值出现时间
            "mobile_optimization": False  # 移动端频响优化通过EBU R128标准
        }
    
    def evaluate(self, data):
        """
        评价流媒体适配
        
        评分标准：
        - 前奏吸引力峰值出现在0-5秒（+4分）
        - 移动端频响优化通过EBU R128标准（+3分）
        
        满分：7分
        """
        score = 0
        details = {}
        
        # 获取参数
        intro_hook_timing = data.market_data.get("streaming_adaptation", {}).get("intro_hook_timing", 0)
        mobile_optimization = data.market_data.get("streaming_adaptation", {}).get("mobile_optimization", False)
        
        # 评分计算
        # 1. 前奏吸引力峰值出现在0-5秒（+4分）
        if 0 <= intro_hook_timing <= 5:
            score += 4
            details["intro_hook_timing"] = f"前奏吸引力峰值出现在{intro_hook_timing}秒，符合0-5秒要求，+4分"
        else:
            details["intro_hook_timing"] = f"前奏吸引力峰值出现在{intro_hook_timing}秒，不符合要求，+0分"
        
        # 2. 移动端频响优化通过EBU R128标准（+3分）
        if mobile_optimization:
            score += 3
            details["mobile_optimization"] = "移动端频响优化通过EBU R128标准，+3分"
        else:
            details["mobile_optimization"] = "移动端频响优化未通过EBU R128标准，+0分"
        
        # 标准化分数到100分制
        normalized_score = (score / 7) * 100
        
        return {
            "score": normalized_score,
            "details": details
        }
