#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
增强版FLAC格式插件 - 支持各种版本的FLAC格式

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import io
import struct
import logging
from typing import Tuple, Optional, Dict, Any, List

from utils.audio_format_plugin import AudioFormatPlugin

# 尝试导入各种FLAC处理库
backends = {}

try:
    import mutagen
    from mutagen.flac import FLAC
    backends["mutagen"] = True
except ImportError:
    backends["mutagen"] = False

try:
    import soundfile
    backends["soundfile"] = True
except ImportError:
    backends["soundfile"] = False

try:
    import pydub
    backends["pydub"] = True
except ImportError:
    backends["pydub"] = False

try:
    import librosa
    backends["librosa"] = True
except ImportError:
    backends["librosa"] = False

try:
    import miniaudio
    backends["miniaudio"] = True
except ImportError:
    backends["miniaudio"] = False

try:
    import tinytag
    backends["tinytag"] = True
except ImportError:
    backends["tinytag"] = False


class EnhancedFLACPlugin(AudioFormatPlugin):
    """增强版FLAC格式插件 - 支持各种版本的FLAC格式"""
    
    def __init__(self):
        """初始化增强版FLAC格式插件"""
        super().__init__()
        self.name = "增强版FLAC格式"
        self.description = "支持各种版本的FLAC无损音频格式，包括老版本和最新版本"
        self.extensions = ["flac"]
        self.mime_types = ["audio/flac", "audio/x-flac"]
        self.versions = ["FLAC 0.x", "FLAC 1.0", "FLAC 1.1", "FLAC 1.2", "FLAC 1.3"]
        self.priority = 90  # 高优先级
    
    def _load_backends(self) -> Dict[str, bool]:
        """加载后端库"""
        return backends
    
    def can_handle(self, file_path: str) -> bool:
        """
        检查是否可以处理指定文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            bool: 是否可以处理
        """
        if not os.path.exists(file_path):
            return False
        
        ext = self.get_file_extension(file_path)
        if ext not in self.extensions:
            return False
        
        # 尝试使用多种方法检测FLAC格式
        try:
            # 方法1: 检查文件头
            with open(file_path, 'rb') as f:
                header = f.read(4)
                # 检查FLAC文件头标识
                if header == b'fLaC':
                    return True
            
            # 方法2: 使用mutagen
            if backends["mutagen"]:
                try:
                    FLAC(file_path)
                    return True
                except:
                    pass
            
            # 方法3: 使用soundfile
            if backends["soundfile"]:
                try:
                    info = soundfile.info(file_path)
                    if info.format == 'FLAC':
                        return True
                except:
                    pass
            
            # 方法4: 使用tinytag
            if backends["tinytag"]:
                try:
                    tag = tinytag.TinyTag.get(file_path)
                    if tag.bitrate > 0:
                        return True
                except:
                    pass
            
            # 方法5: 使用pydub
            if backends["pydub"]:
                try:
                    pydub.AudioSegment.from_file(file_path, format="flac")
                    return True
                except:
                    pass
            
            # 方法6: 使用miniaudio
            if backends["miniaudio"]:
                try:
                    miniaudio.decode_file(file_path)
                    return True
                except:
                    pass
            
            # 方法7: 使用librosa
            if backends["librosa"]:
                try:
                    librosa.load(file_path, sr=None, mono=True, offset=0.0, duration=0.1)
                    return True
                except:
                    pass
            
            return False
        except Exception as e:
            self.logger.error(f"检查FLAC文件失败: {e}")
            return False
    
    def get_version(self, file_path: str) -> str:
        """
        获取FLAC文件版本
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件版本
        """
        try:
            # 尝试读取FLAC头信息
            with open(file_path, 'rb') as f:
                # 检查FLAC标识
                header = f.read(4)
                if header != b'fLaC':
                    return "Unknown"
                
                # 读取STREAMINFO元数据块
                metadata_header = f.read(4)
                if len(metadata_header) < 4:
                    return "Unknown"
                
                # 检查是否是STREAMINFO块（类型0）
                block_type = metadata_header[0] & 0x7F
                if block_type != 0:
                    return "Unknown"
                
                # 读取块大小
                block_size = (metadata_header[1] << 16) | (metadata_header[2] << 8) | metadata_header[3]
                
                # 读取STREAMINFO数据
                streaminfo = f.read(block_size)
                if len(streaminfo) < block_size:
                    return "Unknown"
                
                # 解析最小/最大块大小
                min_block_size = (streaminfo[0] << 8) | streaminfo[1]
                max_block_size = (streaminfo[2] << 8) | streaminfo[3]
                
                # 根据块大小推断版本
                if min_block_size == 0 and max_block_size == 0:
                    return "FLAC 0.x"
                elif max_block_size <= 4608:
                    return "FLAC 1.0"
                elif max_block_size <= 16384:
                    return "FLAC 1.1"
                else:
                    return "FLAC 1.2+"
            
        except Exception as e:
            self.logger.error(f"获取FLAC版本失败: {e}")
            return "Unknown"
    
    def load_audio(self, file_path: str) -> Tuple[Optional[Any], Optional[int]]:
        """
        加载音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Tuple[Optional[Any], Optional[int]]: 音频数据和采样率
        """
        # 尝试使用多种方法加载FLAC
        errors = []
        
        # 方法1: 使用librosa
        if backends["librosa"]:
            try:
                audio_data, sample_rate = librosa.load(file_path, sr=None)
                return audio_data, sample_rate
            except Exception as e:
                errors.append(f"librosa加载失败: {e}")
        
        # 方法2: 使用soundfile
        if backends["soundfile"]:
            try:
                audio_data, sample_rate = soundfile.read(file_path)
                return audio_data, sample_rate
            except Exception as e:
                errors.append(f"soundfile加载失败: {e}")
        
        # 方法3: 使用pydub
        if backends["pydub"]:
            try:
                audio = pydub.AudioSegment.from_file(file_path, format="flac")
                sample_rate = audio.frame_rate
                # 转换为numpy数组
                import numpy as np
                audio_data = np.array(audio.get_array_of_samples()) / 32768.0  # 归一化到[-1, 1]
                if audio.channels == 2:
                    audio_data = audio_data.reshape((-1, 2))
                return audio_data, sample_rate
            except Exception as e:
                errors.append(f"pydub加载失败: {e}")
        
        # 方法4: 使用miniaudio
        if backends["miniaudio"]:
            try:
                audio = miniaudio.decode_file(file_path)
                import numpy as np
                audio_data = np.frombuffer(audio.samples, dtype=np.float32)
                if audio.nchannels == 2:
                    audio_data = audio_data.reshape((-1, 2))
                return audio_data, audio.sample_rate
            except Exception as e:
                errors.append(f"miniaudio加载失败: {e}")
        
        # 记录所有错误
        if errors:
            self.logger.error(f"加载FLAC文件失败: {'; '.join(errors)}")
        
        return None, None
    
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        获取音频元数据
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Dict[str, Any]: 元数据字典
        """
        metadata = {
            "title": "",
            "artist": "",
            "album": "",
            "year": "",
            "genre": "",
            "duration": 0,
            "bitrate": 0,
            "sample_rate": 0,
            "channels": 0,
            "bits_per_sample": 0
        }
        
        errors = []
        
        # 方法1: 使用mutagen
        if backends["mutagen"]:
            try:
                audio = FLAC(file_path)
                
                # 获取Vorbis注释
                if "title" in audio:
                    metadata["title"] = audio["title"][0]
                
                if "artist" in audio:
                    metadata["artist"] = audio["artist"][0]
                
                if "album" in audio:
                    metadata["album"] = audio["album"][0]
                
                if "date" in audio:
                    metadata["year"] = audio["date"][0]
                
                if "genre" in audio:
                    metadata["genre"] = audio["genre"][0]
                
                # 技术信息
                metadata["duration"] = audio.info.length
                metadata["sample_rate"] = audio.info.sample_rate
                metadata["channels"] = audio.info.channels
                metadata["bits_per_sample"] = audio.info.bits_per_sample
                
                # 计算比特率
                if audio.info.length > 0:
                    file_size = os.path.getsize(file_path)
                    metadata["bitrate"] = int((file_size * 8) / audio.info.length)
                
                return metadata
            except Exception as e:
                errors.append(f"mutagen获取元数据失败: {e}")
        
        # 方法2: 使用soundfile
        if backends["soundfile"]:
            try:
                info = soundfile.info(file_path)
                metadata["duration"] = info.duration
                metadata["sample_rate"] = info.samplerate
                metadata["channels"] = info.channels
                metadata["format"] = info.format
                metadata["subtype"] = info.subtype
                
                # 计算比特率
                if info.duration > 0:
                    file_size = os.path.getsize(file_path)
                    metadata["bitrate"] = int((file_size * 8) / info.duration)
                
                return metadata
            except Exception as e:
                errors.append(f"soundfile获取元数据失败: {e}")
        
        # 方法3: 使用tinytag
        if backends["tinytag"]:
            try:
                tag = tinytag.TinyTag.get(file_path)
                metadata["title"] = tag.title or ""
                metadata["artist"] = tag.artist or ""
                metadata["album"] = tag.album or ""
                metadata["year"] = tag.year or ""
                metadata["genre"] = tag.genre or ""
                metadata["duration"] = tag.duration
                metadata["bitrate"] = tag.bitrate
                metadata["sample_rate"] = tag.samplerate
                metadata["channels"] = tag.channels
                
                return metadata
            except Exception as e:
                errors.append(f"tinytag获取元数据失败: {e}")
        
        # 记录所有错误
        if errors:
            self.logger.error(f"获取FLAC元数据失败: {'; '.join(errors)}")
        
        # 如果所有方法都失败，尝试从文件名获取标题
        if not metadata["title"]:
            metadata["title"] = os.path.basename(file_path).split('.')[0]
        
        return metadata
    
    def get_lyrics(self, file_path: str) -> Optional[str]:
        """
        获取歌词
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Optional[str]: 歌词文本
        """
        errors = []
        
        # 方法1: 使用mutagen从Vorbis注释中获取歌词
        if backends["mutagen"]:
            try:
                audio = FLAC(file_path)
                
                # 检查LYRICS标签
                if "lyrics" in audio:
                    return audio["lyrics"][0]
                
                # 检查UNSYNCEDLYRICS标签
                if "unsyncedlyrics" in audio:
                    return audio["unsyncedlyrics"][0]
            except Exception as e:
                errors.append(f"mutagen获取歌词失败: {e}")
        
        # 方法2: 尝试查找同名LRC文件
        try:
            lrc_path = os.path.splitext(file_path)[0] + ".lrc"
            if os.path.exists(lrc_path):
                with open(lrc_path, 'r', encoding='utf-8') as f:
                    return f.read()
        except Exception as e:
            errors.append(f"读取LRC文件失败: {e}")
        
        # 方法3: 尝试查找同名TXT文件
        try:
            txt_path = os.path.splitext(file_path)[0] + ".txt"
            if os.path.exists(txt_path):
                with open(txt_path, 'r', encoding='utf-8') as f:
                    return f.read()
        except Exception as e:
            errors.append(f"读取TXT文件失败: {e}")
        
        # 记录所有错误
        if errors:
            self.logger.debug(f"获取FLAC歌词失败: {'; '.join(errors)}")
        
        return None
