2025-05-18 22:40:57,784 - AudioFormatManager - INFO - 注册插件: mp3 (增强版MP3格式)
2025-05-18 22:40:57,785 - AudioFormatManager - INFO - 注册插件: flac (增强版FLAC格式)
2025-05-18 22:40:57,785 - AudioFormatManager - INFO - 注册插件: universal (通用音频格式)
2025-05-18 22:41:13,052 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 03.最动听.[www.8WQ.com] - 房祖名.wma
2025-05-18 22:41:13,052 - MainWindow - INFO - 音频格式: 通用音频格式, 版本: WMA format
2025-05-18 22:41:13,057 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 03.最动听.[www.8WQ.com] - 房祖名.wma
2025-05-18 22:41:41,373 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 03.最动听.[www.8WQ.com] - 房祖名.wma
2025-05-18 22:46:25,658 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 03.最动听.[www.8WQ.com] - 房祖名.wma
2025-05-18 22:46:58,136 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 03.最动听.[www.8WQ.com] - 房祖名.wma
2025-05-18 22:47:16,684 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 03.最动听.[www.8WQ.com] - 房祖名.wma
2025-05-18 22:47:41,032 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 03.最动听.[www.8WQ.com] - 房祖名.wma
2025-05-18 22:47:43,241 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 03.最动听.[www.8WQ.com] - 房祖名.wma
2025-05-18 22:47:44,800 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 03.最动听.[www.8WQ.com] - 房祖名.wma
2025-05-18 22:47:45,366 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 03.最动听.[www.8WQ.com] - 房祖名.wma
2025-05-18 22:47:51,450 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:47:51,451 - MainWindow - INFO - 音频格式: 增强版MP3格式, 版本: Unknown
2025-05-18 22:47:51,455 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:47:55,324 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:48:06,281 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:48:07,403 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:48:07,645 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:48:07,890 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:48:15,448 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:48:15,676 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:48:15,911 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:48:30,419 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:52:23,690 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:52:24,323 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 04.吻我的样子[yyrl.com] - 李冰冰.mp3
2025-05-18 22:52:33,104 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 10.过敏 - 杨丞琳.wma
2025-05-18 22:52:33,104 - MainWindow - INFO - 音频格式: 通用音频格式, 版本: WMA format
2025-05-18 22:52:33,108 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 10.过敏 - 杨丞琳.wma
2025-05-18 22:52:36,237 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 10.过敏 - 杨丞琳.wma
2025-05-18 22:52:37,825 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 10.过敏 - 杨丞琳.wma
2025-05-18 22:52:38,060 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 10.过敏 - 杨丞琳.wma
2025-05-18 23:22:53,787 - AudioFormatManager - INFO - 注册插件: mp3 (增强版MP3格式)
2025-05-18 23:22:53,789 - AudioFormatManager - INFO - 注册插件: flac (增强版FLAC格式)
2025-05-18 23:22:53,791 - AudioFormatManager - INFO - 注册插件: universal (通用音频格式)
2025-05-18 23:23:01,152 - MainWindow - INFO - 加载插件参数到树状结构
2025-05-18 23:23:01,241 - MainWindow - INFO - 添加插件: technical.harmony_progression.HarmonyProgressionPlugin, 启用状态: True
2025-05-18 23:23:01,303 - MainWindow - INFO - 添加插件: technical.melody_structure.MelodyStructurePlugin, 启用状态: True
2025-05-18 23:23:01,392 - MainWindow - INFO - 添加插件: emotional.emotion_delivery.EmotionDeliveryPlugin, 启用状态: True
2025-05-18 23:23:01,420 - MainWindow - INFO - 添加插件: emotional.lyrics_analysis.LyricsAnalysisPlugin, 启用状态: True
2025-05-18 23:23:01,456 - MainWindow - INFO - 添加插件: market.streaming_adaptation.StreamingAdaptationPlugin, 启用状态: True
2025-05-18 23:23:01,584 - MainWindow - INFO - 添加插件: cultural.style_fusion.StyleFusionPlugin, 启用状态: True
2025-05-18 23:23:01,687 - MainWindow - INFO - 添加插件: production.recording_quality.RecordingQualityPlugin, 启用状态: True
2025-05-18 23:23:49,400 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 大海.mp3
2025-05-18 23:23:49,400 - MainWindow - INFO - 音频格式: 增强版MP3格式, 版本: MPEG 1 Layer III
2025-05-18 23:23:49,404 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 大海.mp3
2025-05-18 23:23:54,163 - MainWindow - INFO - 切换插件 technical.harmony_progression.HarmonyProgressionPlugin 启用状态: False
2025-05-18 23:23:54,829 - MainWindow - INFO - 切换插件 technical.melody_structure.MelodyStructurePlugin 启用状态: False
2025-05-18 23:23:56,559 - MainWindow - INFO - 切换插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 启用状态: False
2025-05-18 23:23:57,398 - MainWindow - INFO - 切换插件 emotional.emotion_delivery.EmotionDeliveryPlugin 启用状态: False
2025-05-18 23:24:05,418 - MainWindow - INFO - 切换插件 market.streaming_adaptation.StreamingAdaptationPlugin 启用状态: False
2025-05-18 23:24:06,153 - MainWindow - INFO - 切换插件 cultural.style_fusion.StyleFusionPlugin 启用状态: False
2025-05-18 23:24:07,173 - MainWindow - INFO - 切换插件 production.recording_quality.RecordingQualityPlugin 启用状态: False
2025-05-18 23:24:12,776 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 大海.mp3
2025-05-18 23:24:15,775 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 大海.mp3
2025-05-18 23:24:17,538 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 大海.mp3
2025-05-18 23:24:18,640 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 大海.mp3
2025-05-18 23:24:19,516 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 大海.mp3
2025-05-18 23:24:21,990 - MainWindow - INFO - 切换插件 technical.harmony_progression.HarmonyProgressionPlugin 启用状态: True
2025-05-18 23:24:24,003 - MainWindow - INFO - 切换插件 technical.harmony_progression.HarmonyProgressionPlugin 启用状态: False
2025-05-18 23:24:24,913 - MainWindow - INFO - 切换插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 启用状态: True
2025-05-18 23:24:25,436 - MainWindow - INFO - 切换插件 emotional.emotion_delivery.EmotionDeliveryPlugin 启用状态: True
2025-05-18 23:24:27,175 - MainWindow - INFO - 开始评价: 大海
2025-05-18 23:24:27,176 - MainWindow - INFO - 已启用的插件: ['emotional.emotion_delivery.EmotionDeliveryPlugin', 'emotional.lyrics_analysis.LyricsAnalysisPlugin']
2025-05-18 23:24:27,196 - Evaluator - INFO - 开始评价，数据: 大海
2025-05-18 23:24:27,197 - Evaluator - INFO - 使用所有已启用的插件，共 2 个
2025-05-18 23:24:27,198 - Evaluator - INFO - 插件: emotional.emotion_delivery.EmotionDeliveryPlugin (情绪传达), 维度: emotional, 分类: emotion_delivery
2025-05-18 23:24:27,199 - Evaluator - INFO - 插件: emotional.lyrics_analysis.LyricsAnalysisPlugin (歌词分析), 维度: emotional, 分类: lyrics_analysis
2025-05-18 23:24:27,200 - Evaluator - INFO - 执行插件 emotional.emotion_delivery.EmotionDeliveryPlugin 的评价
2025-05-18 23:24:27,200 - Evaluator - INFO - 插件 emotional.emotion_delivery.EmotionDeliveryPlugin 评价结果: 分数=0.0
2025-05-18 23:24:27,201 - Evaluator - INFO - 执行插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 的评价
2025-05-18 23:24:27,201 - LyricsAnalysisPlugin - INFO - 开始评价歌词质量: 大海
2025-05-18 23:24:27,202 - LyricsAnalysisPlugin - INFO - 歌词文本长度: 0
2025-05-18 23:24:27,202 - LyricsAnalysisPlugin - WARNING - 没有歌词文本
2025-05-18 23:24:27,203 - LyricsAnalysisPlugin - INFO - 开始评分计算
2025-05-18 23:24:27,204 - LyricsAnalysisPlugin - INFO - 押韵方案复杂度为0.00，不符合要求，+0分
2025-05-18 23:24:27,204 - LyricsAnalysisPlugin - INFO - 隐喻密度为每0.0行1个，不符合要求，+0分
2025-05-18 23:24:27,206 - LyricsAnalysisPlugin - INFO - 词汇丰富度为0.00，不符合要求，+0分
2025-05-18 23:24:27,206 - LyricsAnalysisPlugin - INFO - 国际传播潜力为0.00，不符合要求，+0分
2025-05-18 23:24:27,207 - LyricsAnalysisPlugin - INFO - 评分计算完成，原始分数: 0/10，标准化分数: 0.0/100
2025-05-18 23:24:27,207 - Evaluator - INFO - 插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 评价结果: 分数=0.0
2025-05-18 23:24:27,208 - Evaluator - INFO - 评价完成，共 2 个结果
2025-05-18 23:24:27,208 - MainWindow - INFO - 评价结果: 总分=0.00, 维度分数={'emotional': 0.0}
2025-05-18 23:24:36,862 - MainWindow - INFO - 切换插件 market.streaming_adaptation.StreamingAdaptationPlugin 启用状态: True
2025-05-18 23:24:44,679 - MainWindow - INFO - 开始评价: 大海
2025-05-18 23:24:44,680 - MainWindow - INFO - 已启用的插件: ['emotional.emotion_delivery.EmotionDeliveryPlugin', 'emotional.lyrics_analysis.LyricsAnalysisPlugin', 'market.streaming_adaptation.StreamingAdaptationPlugin']
2025-05-18 23:24:44,699 - Evaluator - INFO - 开始评价，数据: 大海
2025-05-18 23:24:44,700 - Evaluator - INFO - 使用所有已启用的插件，共 3 个
2025-05-18 23:24:44,700 - Evaluator - INFO - 插件: emotional.emotion_delivery.EmotionDeliveryPlugin (情绪传达), 维度: emotional, 分类: emotion_delivery
2025-05-18 23:24:44,700 - Evaluator - INFO - 插件: emotional.lyrics_analysis.LyricsAnalysisPlugin (歌词分析), 维度: emotional, 分类: lyrics_analysis
2025-05-18 23:24:44,700 - Evaluator - INFO - 插件: market.streaming_adaptation.StreamingAdaptationPlugin (流媒体适配), 维度: market, 分类: streaming_adaptation
2025-05-18 23:24:44,702 - Evaluator - INFO - 执行插件 emotional.emotion_delivery.EmotionDeliveryPlugin 的评价
2025-05-18 23:24:44,703 - Evaluator - INFO - 插件 emotional.emotion_delivery.EmotionDeliveryPlugin 评价结果: 分数=0.0
2025-05-18 23:24:44,704 - Evaluator - INFO - 执行插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 的评价
2025-05-18 23:24:44,704 - LyricsAnalysisPlugin - INFO - 开始评价歌词质量: 大海
2025-05-18 23:24:44,704 - LyricsAnalysisPlugin - INFO - 歌词文本长度: 0
2025-05-18 23:24:44,705 - LyricsAnalysisPlugin - WARNING - 没有歌词文本
2025-05-18 23:24:44,705 - LyricsAnalysisPlugin - INFO - 开始评分计算
2025-05-18 23:24:44,705 - LyricsAnalysisPlugin - INFO - 押韵方案复杂度为0.00，不符合要求，+0分
2025-05-18 23:24:44,705 - LyricsAnalysisPlugin - INFO - 隐喻密度为每0.0行1个，不符合要求，+0分
2025-05-18 23:24:44,706 - LyricsAnalysisPlugin - INFO - 词汇丰富度为0.00，不符合要求，+0分
2025-05-18 23:24:44,706 - LyricsAnalysisPlugin - INFO - 国际传播潜力为0.00，不符合要求，+0分
2025-05-18 23:24:44,707 - LyricsAnalysisPlugin - INFO - 评分计算完成，原始分数: 0/10，标准化分数: 0.0/100
2025-05-18 23:24:44,707 - Evaluator - INFO - 插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 评价结果: 分数=0.0
2025-05-18 23:24:44,707 - Evaluator - INFO - 执行插件 market.streaming_adaptation.StreamingAdaptationPlugin 的评价
2025-05-18 23:24:44,708 - Evaluator - INFO - 插件 market.streaming_adaptation.StreamingAdaptationPlugin 评价结果: 分数=57.14285714285714
2025-05-18 23:24:44,708 - Evaluator - INFO - 评价完成，共 3 个结果
2025-05-18 23:24:44,709 - MainWindow - INFO - 评价结果: 总分=25.40, 维度分数={'emotional': 0.0, 'market': 57.14285714285714}
2025-05-18 23:24:51,978 - MainWindow - INFO - 切换插件 emotional.emotion_delivery.EmotionDeliveryPlugin 启用状态: False
2025-05-18 23:24:52,504 - MainWindow - INFO - 切换插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 启用状态: False
2025-05-18 23:24:53,395 - MainWindow - INFO - 切换插件 market.streaming_adaptation.StreamingAdaptationPlugin 启用状态: False
2025-05-18 23:24:54,556 - MainWindow - INFO - 切换插件 technical.harmony_progression.HarmonyProgressionPlugin 启用状态: True
2025-05-18 23:24:55,431 - MainWindow - INFO - 切换插件 technical.melody_structure.MelodyStructurePlugin 启用状态: True
2025-05-18 23:25:19,915 - MainWindow - INFO - 切换插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 启用状态: True
2025-05-18 23:25:20,978 - MainWindow - INFO - 切换插件 technical.melody_structure.MelodyStructurePlugin 启用状态: False
2025-05-18 23:25:21,535 - MainWindow - INFO - 切换插件 technical.harmony_progression.HarmonyProgressionPlugin 启用状态: False
2025-05-18 23:25:22,899 - MainWindow - INFO - 切换插件 cultural.style_fusion.StyleFusionPlugin 启用状态: True
2025-05-18 23:25:25,328 - MainWindow - INFO - 开始评价: 大海
2025-05-18 23:25:25,329 - MainWindow - INFO - 已启用的插件: ['emotional.lyrics_analysis.LyricsAnalysisPlugin', 'cultural.style_fusion.StyleFusionPlugin']
2025-05-18 23:25:25,346 - Evaluator - INFO - 开始评价，数据: 大海
2025-05-18 23:25:25,347 - Evaluator - INFO - 使用所有已启用的插件，共 2 个
2025-05-18 23:25:25,347 - Evaluator - INFO - 插件: emotional.lyrics_analysis.LyricsAnalysisPlugin (歌词分析), 维度: emotional, 分类: lyrics_analysis
2025-05-18 23:25:25,348 - Evaluator - INFO - 插件: cultural.style_fusion.StyleFusionPlugin (风格融合), 维度: cultural, 分类: style_fusion
2025-05-18 23:25:25,348 - Evaluator - INFO - 执行插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 的评价
2025-05-18 23:25:25,349 - LyricsAnalysisPlugin - INFO - 开始评价歌词质量: 大海
2025-05-18 23:25:25,349 - LyricsAnalysisPlugin - INFO - 歌词文本长度: 0
2025-05-18 23:25:25,349 - LyricsAnalysisPlugin - WARNING - 没有歌词文本
2025-05-18 23:25:25,350 - LyricsAnalysisPlugin - INFO - 开始评分计算
2025-05-18 23:25:25,351 - LyricsAnalysisPlugin - INFO - 押韵方案复杂度为0.00，不符合要求，+0分
2025-05-18 23:25:25,352 - LyricsAnalysisPlugin - INFO - 隐喻密度为每0.0行1个，不符合要求，+0分
2025-05-18 23:25:25,352 - LyricsAnalysisPlugin - INFO - 词汇丰富度为0.00，不符合要求，+0分
2025-05-18 23:25:25,352 - LyricsAnalysisPlugin - INFO - 国际传播潜力为0.00，不符合要求，+0分
2025-05-18 23:25:25,352 - LyricsAnalysisPlugin - INFO - 评分计算完成，原始分数: 0/10，标准化分数: 0.0/100
2025-05-18 23:25:25,353 - Evaluator - INFO - 插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 评价结果: 分数=0.0
2025-05-18 23:25:25,353 - Evaluator - INFO - 执行插件 cultural.style_fusion.StyleFusionPlugin 的评价
2025-05-18 23:25:25,354 - Evaluator - INFO - 插件 cultural.style_fusion.StyleFusionPlugin 评价结果: 分数=0.0
2025-05-18 23:25:25,354 - Evaluator - INFO - 评价完成，共 2 个结果
2025-05-18 23:25:25,355 - MainWindow - INFO - 评价结果: 总分=0.00, 维度分数={'emotional': 0.0, 'cultural': 0.0}
2025-05-18 23:25:28,834 - MainWindow - INFO - 切换插件 technical.harmony_progression.HarmonyProgressionPlugin 启用状态: True
2025-05-18 23:25:29,275 - MainWindow - INFO - 切换插件 technical.melody_structure.MelodyStructurePlugin 启用状态: True
2025-05-18 23:25:29,791 - MainWindow - INFO - 切换插件 emotional.emotion_delivery.EmotionDeliveryPlugin 启用状态: True
2025-05-18 23:25:30,449 - MainWindow - INFO - 切换插件 market.streaming_adaptation.StreamingAdaptationPlugin 启用状态: True
2025-05-18 23:25:31,193 - MainWindow - INFO - 切换插件 production.recording_quality.RecordingQualityPlugin 启用状态: True
2025-05-18 23:25:33,171 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 大海.mp3
2025-05-18 23:25:34,003 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 大海.mp3
2025-05-18 23:25:35,306 - MainWindow - INFO - 开始评价: 大海
2025-05-18 23:25:35,306 - MainWindow - INFO - 已启用的插件: ['technical.harmony_progression.HarmonyProgressionPlugin', 'technical.melody_structure.MelodyStructurePlugin', 'emotional.emotion_delivery.EmotionDeliveryPlugin', 'emotional.lyrics_analysis.LyricsAnalysisPlugin', 'market.streaming_adaptation.StreamingAdaptationPlugin', 'cultural.style_fusion.StyleFusionPlugin', 'production.recording_quality.RecordingQualityPlugin']
2025-05-18 23:25:35,322 - Evaluator - INFO - 开始评价，数据: 大海
2025-05-18 23:25:35,322 - Evaluator - INFO - 使用所有已启用的插件，共 7 个
2025-05-18 23:25:35,322 - Evaluator - INFO - 插件: technical.harmony_progression.HarmonyProgressionPlugin (和声进行), 维度: technical, 分类: harmony_progression
2025-05-18 23:25:35,323 - Evaluator - INFO - 插件: technical.melody_structure.MelodyStructurePlugin (旋律结构), 维度: technical, 分类: melody_structure
2025-05-18 23:25:35,323 - Evaluator - INFO - 插件: emotional.emotion_delivery.EmotionDeliveryPlugin (情绪传达), 维度: emotional, 分类: emotion_delivery
2025-05-18 23:25:35,324 - Evaluator - INFO - 插件: emotional.lyrics_analysis.LyricsAnalysisPlugin (歌词分析), 维度: emotional, 分类: lyrics_analysis
2025-05-18 23:25:35,324 - Evaluator - INFO - 插件: market.streaming_adaptation.StreamingAdaptationPlugin (流媒体适配), 维度: market, 分类: streaming_adaptation
2025-05-18 23:25:35,324 - Evaluator - INFO - 插件: cultural.style_fusion.StyleFusionPlugin (风格融合), 维度: cultural, 分类: style_fusion
2025-05-18 23:25:35,325 - Evaluator - INFO - 插件: production.recording_quality.RecordingQualityPlugin (录音质量), 维度: production, 分类: recording_quality
2025-05-18 23:25:35,325 - Evaluator - INFO - 执行插件 technical.harmony_progression.HarmonyProgressionPlugin 的评价
2025-05-18 23:25:35,325 - Evaluator - INFO - 插件 technical.harmony_progression.HarmonyProgressionPlugin 评价结果: 分数=33.33333333333333
2025-05-18 23:25:35,326 - Evaluator - INFO - 执行插件 technical.melody_structure.MelodyStructurePlugin 的评价
2025-05-18 23:25:35,326 - Evaluator - INFO - 插件 technical.melody_structure.MelodyStructurePlugin 评价结果: 分数=0.0
2025-05-18 23:25:35,326 - Evaluator - INFO - 执行插件 emotional.emotion_delivery.EmotionDeliveryPlugin 的评价
2025-05-18 23:25:35,327 - Evaluator - INFO - 插件 emotional.emotion_delivery.EmotionDeliveryPlugin 评价结果: 分数=0.0
2025-05-18 23:25:35,327 - Evaluator - INFO - 执行插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 的评价
2025-05-18 23:25:35,327 - LyricsAnalysisPlugin - INFO - 开始评价歌词质量: 大海
2025-05-18 23:25:35,327 - LyricsAnalysisPlugin - INFO - 歌词文本长度: 0
2025-05-18 23:25:35,327 - LyricsAnalysisPlugin - WARNING - 没有歌词文本
2025-05-18 23:25:35,328 - LyricsAnalysisPlugin - INFO - 开始评分计算
2025-05-18 23:25:35,328 - LyricsAnalysisPlugin - INFO - 押韵方案复杂度为0.00，不符合要求，+0分
2025-05-18 23:25:35,328 - LyricsAnalysisPlugin - INFO - 隐喻密度为每0.0行1个，不符合要求，+0分
2025-05-18 23:25:35,328 - LyricsAnalysisPlugin - INFO - 词汇丰富度为0.00，不符合要求，+0分
2025-05-18 23:25:35,328 - LyricsAnalysisPlugin - INFO - 国际传播潜力为0.00，不符合要求，+0分
2025-05-18 23:25:35,329 - LyricsAnalysisPlugin - INFO - 评分计算完成，原始分数: 0/10，标准化分数: 0.0/100
2025-05-18 23:25:35,329 - Evaluator - INFO - 插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 评价结果: 分数=0.0
2025-05-18 23:25:35,329 - Evaluator - INFO - 执行插件 market.streaming_adaptation.StreamingAdaptationPlugin 的评价
2025-05-18 23:25:35,329 - Evaluator - INFO - 插件 market.streaming_adaptation.StreamingAdaptationPlugin 评价结果: 分数=57.14285714285714
2025-05-18 23:25:35,330 - Evaluator - INFO - 执行插件 cultural.style_fusion.StyleFusionPlugin 的评价
2025-05-18 23:25:35,330 - Evaluator - INFO - 插件 cultural.style_fusion.StyleFusionPlugin 评价结果: 分数=0.0
2025-05-18 23:25:35,330 - Evaluator - INFO - 执行插件 production.recording_quality.RecordingQualityPlugin 的评价
2025-05-18 23:25:35,331 - Evaluator - INFO - 插件 production.recording_quality.RecordingQualityPlugin 评价结果: 分数=0.0
2025-05-18 23:25:35,331 - Evaluator - INFO - 评价完成，共 7 个结果
2025-05-18 23:25:35,331 - MainWindow - INFO - 评价结果: 总分=16.43, 维度分数={'technical': 16.666666666666664, 'emotional': 0.0, 'market': 57.14285714285714, 'cultural': 0.0, 'production': 0.0}
2025-05-18 23:26:35,537 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 大海.mp3
2025-05-18 23:26:37,355 - MainWindow - INFO - 开始评价: 大海
2025-05-18 23:26:37,356 - MainWindow - INFO - 已启用的插件: ['technical.harmony_progression.HarmonyProgressionPlugin', 'technical.melody_structure.MelodyStructurePlugin', 'emotional.emotion_delivery.EmotionDeliveryPlugin', 'emotional.lyrics_analysis.LyricsAnalysisPlugin', 'market.streaming_adaptation.StreamingAdaptationPlugin', 'cultural.style_fusion.StyleFusionPlugin', 'production.recording_quality.RecordingQualityPlugin']
2025-05-18 23:26:37,372 - Evaluator - INFO - 开始评价，数据: 大海
2025-05-18 23:26:37,372 - Evaluator - INFO - 使用所有已启用的插件，共 7 个
2025-05-18 23:26:37,373 - Evaluator - INFO - 插件: technical.harmony_progression.HarmonyProgressionPlugin (和声进行), 维度: technical, 分类: harmony_progression
2025-05-18 23:26:37,374 - Evaluator - INFO - 插件: technical.melody_structure.MelodyStructurePlugin (旋律结构), 维度: technical, 分类: melody_structure
2025-05-18 23:26:37,374 - Evaluator - INFO - 插件: emotional.emotion_delivery.EmotionDeliveryPlugin (情绪传达), 维度: emotional, 分类: emotion_delivery
2025-05-18 23:26:37,374 - Evaluator - INFO - 插件: emotional.lyrics_analysis.LyricsAnalysisPlugin (歌词分析), 维度: emotional, 分类: lyrics_analysis
2025-05-18 23:26:37,374 - Evaluator - INFO - 插件: market.streaming_adaptation.StreamingAdaptationPlugin (流媒体适配), 维度: market, 分类: streaming_adaptation
2025-05-18 23:26:37,375 - Evaluator - INFO - 插件: cultural.style_fusion.StyleFusionPlugin (风格融合), 维度: cultural, 分类: style_fusion
2025-05-18 23:26:37,375 - Evaluator - INFO - 插件: production.recording_quality.RecordingQualityPlugin (录音质量), 维度: production, 分类: recording_quality
2025-05-18 23:26:37,375 - Evaluator - INFO - 执行插件 technical.harmony_progression.HarmonyProgressionPlugin 的评价
2025-05-18 23:26:37,375 - Evaluator - INFO - 插件 technical.harmony_progression.HarmonyProgressionPlugin 评价结果: 分数=33.33333333333333
2025-05-18 23:26:37,375 - Evaluator - INFO - 执行插件 technical.melody_structure.MelodyStructurePlugin 的评价
2025-05-18 23:26:37,376 - Evaluator - INFO - 插件 technical.melody_structure.MelodyStructurePlugin 评价结果: 分数=0.0
2025-05-18 23:26:37,376 - Evaluator - INFO - 执行插件 emotional.emotion_delivery.EmotionDeliveryPlugin 的评价
2025-05-18 23:26:37,376 - Evaluator - INFO - 插件 emotional.emotion_delivery.EmotionDeliveryPlugin 评价结果: 分数=0.0
2025-05-18 23:26:37,376 - Evaluator - INFO - 执行插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 的评价
2025-05-18 23:26:37,376 - LyricsAnalysisPlugin - INFO - 开始评价歌词质量: 大海
2025-05-18 23:26:37,377 - LyricsAnalysisPlugin - INFO - 歌词文本长度: 0
2025-05-18 23:26:37,377 - LyricsAnalysisPlugin - WARNING - 没有歌词文本
2025-05-18 23:26:37,377 - LyricsAnalysisPlugin - INFO - 开始评分计算
2025-05-18 23:26:37,377 - LyricsAnalysisPlugin - INFO - 押韵方案复杂度为0.00，不符合要求，+0分
2025-05-18 23:26:37,377 - LyricsAnalysisPlugin - INFO - 隐喻密度为每0.0行1个，不符合要求，+0分
2025-05-18 23:26:37,377 - LyricsAnalysisPlugin - INFO - 词汇丰富度为0.00，不符合要求，+0分
2025-05-18 23:26:37,378 - LyricsAnalysisPlugin - INFO - 国际传播潜力为0.00，不符合要求，+0分
2025-05-18 23:26:37,378 - LyricsAnalysisPlugin - INFO - 评分计算完成，原始分数: 0/10，标准化分数: 0.0/100
2025-05-18 23:26:37,378 - Evaluator - INFO - 插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 评价结果: 分数=0.0
2025-05-18 23:26:37,378 - Evaluator - INFO - 执行插件 market.streaming_adaptation.StreamingAdaptationPlugin 的评价
2025-05-18 23:26:37,379 - Evaluator - INFO - 插件 market.streaming_adaptation.StreamingAdaptationPlugin 评价结果: 分数=57.14285714285714
2025-05-18 23:26:37,379 - Evaluator - INFO - 执行插件 cultural.style_fusion.StyleFusionPlugin 的评价
2025-05-18 23:26:37,379 - Evaluator - INFO - 插件 cultural.style_fusion.StyleFusionPlugin 评价结果: 分数=0.0
2025-05-18 23:26:37,379 - Evaluator - INFO - 执行插件 production.recording_quality.RecordingQualityPlugin 的评价
2025-05-18 23:26:37,380 - Evaluator - INFO - 插件 production.recording_quality.RecordingQualityPlugin 评价结果: 分数=0.0
2025-05-18 23:26:37,381 - Evaluator - INFO - 评价完成，共 7 个结果
2025-05-18 23:26:37,381 - MainWindow - INFO - 评价结果: 总分=16.43, 维度分数={'technical': 16.666666666666664, 'emotional': 0.0, 'market': 57.14285714285714, 'cultural': 0.0, 'production': 0.0}
2025-05-18 23:31:08,958 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 陈慧琳-记事本.mp3
2025-05-18 23:31:08,961 - MainWindow - INFO - 音频格式: 增强版MP3格式, 版本: MPEG 1 Layer III
2025-05-18 23:31:08,973 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 陈慧琳-记事本.mp3
2025-05-18 23:31:11,667 - AudioFormatManager - INFO - 使用插件 EnhancedMP3Plugin 处理文件: 陈慧琳-记事本.mp3
2025-05-18 23:31:15,602 - MainWindow - INFO - 开始评价: 陈慧琳-记事本
2025-05-18 23:31:15,603 - MainWindow - INFO - 已启用的插件: ['technical.harmony_progression.HarmonyProgressionPlugin', 'technical.melody_structure.MelodyStructurePlugin', 'emotional.emotion_delivery.EmotionDeliveryPlugin', 'emotional.lyrics_analysis.LyricsAnalysisPlugin', 'market.streaming_adaptation.StreamingAdaptationPlugin', 'cultural.style_fusion.StyleFusionPlugin', 'production.recording_quality.RecordingQualityPlugin']
2025-05-18 23:31:15,619 - Evaluator - INFO - 开始评价，数据: 陈慧琳-记事本
2025-05-18 23:31:15,620 - Evaluator - INFO - 使用所有已启用的插件，共 7 个
2025-05-18 23:31:15,621 - Evaluator - INFO - 插件: technical.harmony_progression.HarmonyProgressionPlugin (和声进行), 维度: technical, 分类: harmony_progression
2025-05-18 23:31:15,622 - Evaluator - INFO - 插件: technical.melody_structure.MelodyStructurePlugin (旋律结构), 维度: technical, 分类: melody_structure
2025-05-18 23:31:15,622 - Evaluator - INFO - 插件: emotional.emotion_delivery.EmotionDeliveryPlugin (情绪传达), 维度: emotional, 分类: emotion_delivery
2025-05-18 23:31:15,623 - Evaluator - INFO - 插件: emotional.lyrics_analysis.LyricsAnalysisPlugin (歌词分析), 维度: emotional, 分类: lyrics_analysis
2025-05-18 23:31:15,623 - Evaluator - INFO - 插件: market.streaming_adaptation.StreamingAdaptationPlugin (流媒体适配), 维度: market, 分类: streaming_adaptation
2025-05-18 23:31:15,624 - Evaluator - INFO - 插件: cultural.style_fusion.StyleFusionPlugin (风格融合), 维度: cultural, 分类: style_fusion
2025-05-18 23:31:15,624 - Evaluator - INFO - 插件: production.recording_quality.RecordingQualityPlugin (录音质量), 维度: production, 分类: recording_quality
2025-05-18 23:31:15,625 - Evaluator - INFO - 执行插件 technical.harmony_progression.HarmonyProgressionPlugin 的评价
2025-05-18 23:31:15,625 - Evaluator - INFO - 插件 technical.harmony_progression.HarmonyProgressionPlugin 评价结果: 分数=33.33333333333333
2025-05-18 23:31:15,625 - Evaluator - INFO - 执行插件 technical.melody_structure.MelodyStructurePlugin 的评价
2025-05-18 23:31:15,626 - Evaluator - INFO - 插件 technical.melody_structure.MelodyStructurePlugin 评价结果: 分数=0.0
2025-05-18 23:31:15,627 - Evaluator - INFO - 执行插件 emotional.emotion_delivery.EmotionDeliveryPlugin 的评价
2025-05-18 23:31:15,627 - Evaluator - INFO - 插件 emotional.emotion_delivery.EmotionDeliveryPlugin 评价结果: 分数=0.0
2025-05-18 23:31:15,627 - Evaluator - INFO - 执行插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 的评价
2025-05-18 23:31:15,628 - LyricsAnalysisPlugin - INFO - 开始评价歌词质量: 陈慧琳-记事本
2025-05-18 23:31:15,629 - LyricsAnalysisPlugin - INFO - 歌词文本长度: 0
2025-05-18 23:31:15,629 - LyricsAnalysisPlugin - WARNING - 没有歌词文本
2025-05-18 23:31:15,630 - LyricsAnalysisPlugin - INFO - 开始评分计算
2025-05-18 23:31:15,631 - LyricsAnalysisPlugin - INFO - 押韵方案复杂度为0.00，不符合要求，+0分
2025-05-18 23:31:15,631 - LyricsAnalysisPlugin - INFO - 隐喻密度为每0.0行1个，不符合要求，+0分
2025-05-18 23:31:15,631 - LyricsAnalysisPlugin - INFO - 词汇丰富度为0.00，不符合要求，+0分
2025-05-18 23:31:15,631 - LyricsAnalysisPlugin - INFO - 国际传播潜力为0.00，不符合要求，+0分
2025-05-18 23:31:15,632 - LyricsAnalysisPlugin - INFO - 评分计算完成，原始分数: 0/10，标准化分数: 0.0/100
2025-05-18 23:31:15,632 - Evaluator - INFO - 插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 评价结果: 分数=0.0
2025-05-18 23:31:15,633 - Evaluator - INFO - 执行插件 market.streaming_adaptation.StreamingAdaptationPlugin 的评价
2025-05-18 23:31:15,633 - Evaluator - INFO - 插件 market.streaming_adaptation.StreamingAdaptationPlugin 评价结果: 分数=57.14285714285714
2025-05-18 23:31:15,634 - Evaluator - INFO - 执行插件 cultural.style_fusion.StyleFusionPlugin 的评价
2025-05-18 23:31:15,634 - Evaluator - INFO - 插件 cultural.style_fusion.StyleFusionPlugin 评价结果: 分数=0.0
2025-05-18 23:31:15,635 - Evaluator - INFO - 执行插件 production.recording_quality.RecordingQualityPlugin 的评价
2025-05-18 23:31:15,635 - Evaluator - INFO - 插件 production.recording_quality.RecordingQualityPlugin 评价结果: 分数=0.0
2025-05-18 23:31:15,636 - Evaluator - INFO - 评价完成，共 7 个结果
2025-05-18 23:31:15,637 - MainWindow - INFO - 评价结果: 总分=16.43, 维度分数={'technical': 16.666666666666664, 'emotional': 0.0, 'market': 57.14285714285714, 'cultural': 0.0, 'production': 0.0}
2025-05-25 11:09:50,290 - AudioFormatManager - INFO - 注册插件: mp3 (增强版MP3格式)
2025-05-25 11:09:50,295 - AudioFormatManager - INFO - 注册插件: flac (增强版FLAC格式)
2025-05-25 11:09:50,298 - AudioFormatManager - INFO - 注册插件: universal (通用音频格式)
2025-05-25 11:10:01,841 - MainWindow - INFO - 加载插件参数到树状结构
2025-05-25 11:10:01,856 - MainWindow - INFO - 添加插件: technical.harmony_progression.HarmonyProgressionPlugin, 启用状态: True
2025-05-25 11:10:01,860 - MainWindow - INFO - 添加插件: technical.melody_structure.MelodyStructurePlugin, 启用状态: True
2025-05-25 11:10:01,867 - MainWindow - INFO - 添加插件: emotional.emotion_delivery.EmotionDeliveryPlugin, 启用状态: True
2025-05-25 11:10:01,872 - MainWindow - INFO - 添加插件: emotional.lyrics_analysis.LyricsAnalysisPlugin, 启用状态: True
2025-05-25 11:10:01,876 - MainWindow - INFO - 添加插件: market.streaming_adaptation.StreamingAdaptationPlugin, 启用状态: True
2025-05-25 11:10:01,883 - MainWindow - INFO - 添加插件: cultural.style_fusion.StyleFusionPlugin, 启用状态: True
2025-05-25 11:10:01,893 - MainWindow - INFO - 添加插件: production.recording_quality.RecordingQualityPlugin, 启用状态: True
2025-05-25 11:12:49,390 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 爱笑的眼睛 - 徐若萱.wma
2025-05-25 11:12:49,390 - MainWindow - INFO - 音频格式: 通用音频格式, 版本: WMA format
2025-05-25 11:12:49,405 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 爱笑的眼睛 - 徐若萱.wma
2025-05-25 11:13:01,265 - AudioFormatManager - INFO - 使用插件 UniversalAudioPlugin 处理文件: 爱笑的眼睛 - 徐若萱.wma
2025-05-25 11:13:01,280 - MainWindow - INFO - 获取到歌词: 否, 长度: 0
2025-05-25 11:13:01,285 - MainWindow - WARNING - 无法获取歌词，将使用空歌词
2025-05-25 11:13:06,810 - MainWindow - INFO - 开始评价: 爱笑的眼睛 - 徐若萱
2025-05-25 11:13:06,811 - MainWindow - INFO - 已启用的插件: ['technical.harmony_progression.HarmonyProgressionPlugin', 'technical.melody_structure.MelodyStructurePlugin', 'emotional.emotion_delivery.EmotionDeliveryPlugin', 'emotional.lyrics_analysis.LyricsAnalysisPlugin', 'market.streaming_adaptation.StreamingAdaptationPlugin', 'cultural.style_fusion.StyleFusionPlugin', 'production.recording_quality.RecordingQualityPlugin']
2025-05-25 11:13:06,873 - Evaluator - INFO - 开始评价，数据: 爱笑的眼睛 - 徐若萱
2025-05-25 11:13:06,878 - Evaluator - INFO - 使用所有已启用的插件，共 7 个
2025-05-25 11:13:06,884 - Evaluator - INFO - 插件: technical.harmony_progression.HarmonyProgressionPlugin (和声进行), 维度: technical, 分类: harmony_progression
2025-05-25 11:13:06,888 - Evaluator - INFO - 插件: technical.melody_structure.MelodyStructurePlugin (旋律结构), 维度: technical, 分类: melody_structure
2025-05-25 11:13:06,891 - Evaluator - INFO - 插件: emotional.emotion_delivery.EmotionDeliveryPlugin (情绪传达), 维度: emotional, 分类: emotion_delivery
2025-05-25 11:13:06,895 - Evaluator - INFO - 插件: emotional.lyrics_analysis.LyricsAnalysisPlugin (歌词分析), 维度: emotional, 分类: lyrics_analysis
2025-05-25 11:13:06,903 - Evaluator - INFO - 插件: market.streaming_adaptation.StreamingAdaptationPlugin (流媒体适配), 维度: market, 分类: streaming_adaptation
2025-05-25 11:13:06,909 - Evaluator - INFO - 插件: cultural.style_fusion.StyleFusionPlugin (风格融合), 维度: cultural, 分类: style_fusion
2025-05-25 11:13:06,918 - Evaluator - INFO - 插件: production.recording_quality.RecordingQualityPlugin (录音质量), 维度: production, 分类: recording_quality
2025-05-25 11:13:06,923 - Evaluator - INFO - 执行插件 technical.harmony_progression.HarmonyProgressionPlugin 的评价
2025-05-25 11:13:06,927 - Evaluator - INFO - 插件 technical.harmony_progression.HarmonyProgressionPlugin 评价结果: 分数=33.33333333333333
2025-05-25 11:13:06,933 - Evaluator - INFO - 执行插件 technical.melody_structure.MelodyStructurePlugin 的评价
2025-05-25 11:13:06,935 - Evaluator - INFO - 插件 technical.melody_structure.MelodyStructurePlugin 评价结果: 分数=0.0
2025-05-25 11:13:06,938 - Evaluator - INFO - 执行插件 emotional.emotion_delivery.EmotionDeliveryPlugin 的评价
2025-05-25 11:13:06,943 - EmotionDeliveryPlugin - INFO - 开始评价情绪传达: 爱笑的眼睛 - 徐若萱
2025-05-25 11:13:06,948 - EmotionDeliveryPlugin - INFO - 获取到的参数: chorus_range=0, dynamic_range=0, synesthesia_count=0
2025-05-25 11:13:06,950 - EmotionDeliveryPlugin - WARNING - 所有参数都是0，尝试设置默认值
2025-05-25 11:13:06,952 - EmotionDeliveryPlugin - INFO - 从文件路径推断: F:/妈妈MP3里面的音乐以前（误删！！！！！！！）/爱笑的眼睛 - 徐若萱.wma
2025-05-25 11:13:06,954 - EmotionDeliveryPlugin - INFO - 使用默认值: chorus_range=5, dynamic_range=7
2025-05-25 11:13:06,956 - EmotionDeliveryPlugin - INFO - 使用默认通感修辞数量: 3
2025-05-25 11:13:06,958 - EmotionDeliveryPlugin - INFO - 副歌最高音与最低音差为5度，大于0度，+1分
2025-05-25 11:13:06,962 - EmotionDeliveryPlugin - INFO - 动态范围DR值为7，大于0，+1分
2025-05-25 11:13:06,965 - EmotionDeliveryPlugin - INFO - 使用通感修辞3处，大于2处，+1分
2025-05-25 11:13:06,967 - EmotionDeliveryPlugin - INFO - 评分计算完成，原始分数: 3/9，标准化分数: 33.33333333333333/100
2025-05-25 11:13:06,969 - Evaluator - INFO - 插件 emotional.emotion_delivery.EmotionDeliveryPlugin 评价结果: 分数=33.33333333333333
2025-05-25 11:13:06,972 - Evaluator - INFO - 执行插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 的评价
2025-05-25 11:13:06,974 - LyricsAnalysisPlugin - INFO - 开始评价歌词质量: 爱笑的眼睛 - 徐若萱
2025-05-25 11:13:06,978 - LyricsAnalysisPlugin - INFO - 歌词文本长度: 0
2025-05-25 11:13:06,980 - LyricsAnalysisPlugin - WARNING - 没有歌词文本
2025-05-25 11:13:06,983 - LyricsAnalysisPlugin - INFO - 从音频特征推断的参数: rhyme_scheme=0, metaphor_density=0, vocabulary_richness=0, international_appeal=0
2025-05-25 11:13:06,985 - LyricsAnalysisPlugin - INFO - 使用默认押韵方案复杂度: 0.3
2025-05-25 11:13:06,988 - LyricsAnalysisPlugin - INFO - 使用默认隐喻密度: 0.05
2025-05-25 11:13:06,991 - LyricsAnalysisPlugin - INFO - 使用默认词汇丰富度: 0.4
2025-05-25 11:13:06,994 - LyricsAnalysisPlugin - INFO - 使用默认国际传播潜力: 0.2
2025-05-25 11:13:06,997 - LyricsAnalysisPlugin - INFO - 最终参数: rhyme_scheme=0.3, metaphor_density=0.05, vocabulary_richness=0.4, international_appeal=0.2
2025-05-25 11:13:07,001 - LyricsAnalysisPlugin - INFO - 开始评分计算
2025-05-25 11:13:07,003 - LyricsAnalysisPlugin - INFO - 押韵方案复杂度为0.30，不符合要求，+0分
2025-05-25 11:13:07,006 - LyricsAnalysisPlugin - INFO - 隐喻密度为每20.0行1个，不符合要求，+0分
2025-05-25 11:13:07,011 - LyricsAnalysisPlugin - INFO - 词汇丰富度为0.40，不符合要求，+0分
2025-05-25 11:13:07,013 - LyricsAnalysisPlugin - INFO - 国际传播潜力为0.20，不符合要求，+0分
2025-05-25 11:13:07,016 - LyricsAnalysisPlugin - INFO - 评分计算完成，原始分数: 0/10，标准化分数: 0.0/100
2025-05-25 11:13:07,019 - Evaluator - INFO - 插件 emotional.lyrics_analysis.LyricsAnalysisPlugin 评价结果: 分数=0.0
2025-05-25 11:13:07,023 - Evaluator - INFO - 执行插件 market.streaming_adaptation.StreamingAdaptationPlugin 的评价
2025-05-25 11:13:07,026 - Evaluator - INFO - 插件 market.streaming_adaptation.StreamingAdaptationPlugin 评价结果: 分数=57.14285714285714
2025-05-25 11:13:07,028 - Evaluator - INFO - 执行插件 cultural.style_fusion.StyleFusionPlugin 的评价
2025-05-25 11:13:07,031 - Evaluator - INFO - 插件 cultural.style_fusion.StyleFusionPlugin 评价结果: 分数=0.0
2025-05-25 11:13:07,035 - Evaluator - INFO - 执行插件 production.recording_quality.RecordingQualityPlugin 的评价
2025-05-25 11:13:07,041 - Evaluator - INFO - 插件 production.recording_quality.RecordingQualityPlugin 评价结果: 分数=0.0
2025-05-25 11:13:07,044 - Evaluator - INFO - 评价完成，共 7 个结果
2025-05-25 11:13:07,050 - MainWindow - INFO - 评价结果: 总分=20.60, 维度分数={'technical': 16.666666666666664, 'emotional': 16.666666666666664, 'market': 57.14285714285714, 'cultural': 0.0, 'production': 0.0}
