#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
FLAC格式插件

MIT License
Copyright (c) 2025 Music Guru
"""

import os
from typing import Tuple, Optional, Dict, Any

from utils.audio_format_plugin import AudioFormatPlugin

try:
    import librosa
    import librosa.display
    import mutagen
    from mutagen.flac import FLAC
    DEPENDENCIES_AVAILABLE = True
except ImportError:
    DEPENDENCIES_AVAILABLE = False


class FLACPlugin(AudioFormatPlugin):
    """FLAC格式插件"""
    
    def __init__(self):
        """初始化FLAC格式插件"""
        super().__init__()
        self.name = "FLAC格式"
        self.description = "支持FLAC无损音频格式"
        self.extensions = ["flac"]
    
    def can_handle(self, file_path: str) -> bool:
        """
        检查是否可以处理指定文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            bool: 是否可以处理
        """
        if not DEPENDENCIES_AVAILABLE:
            return False
        
        ext = self.get_file_extension(file_path)
        if ext not in self.extensions:
            return False
        
        try:
            # 尝试加载文件头
            with open(file_path, 'rb') as f:
                header = f.read(4)
                # 检查FLAC文件头标识
                return header == b'fLaC'
        except Exception:
            return False
    
    def load_audio(self, file_path: str) -> Tuple[Optional[Any], Optional[int]]:
        """
        加载音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Tuple[Optional[Any], Optional[int]]: 音频数据和采样率
        """
        if not DEPENDENCIES_AVAILABLE:
            return None, None
        
        try:
            audio_data, sample_rate = librosa.load(file_path, sr=None)
            return audio_data, sample_rate
        except Exception as e:
            print(f"加载FLAC文件失败: {e}")
            return None, None
    
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        获取音频元数据
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Dict[str, Any]: 元数据字典
        """
        metadata = {
            "title": "",
            "artist": "",
            "album": "",
            "year": "",
            "genre": "",
            "duration": 0
        }
        
        if not DEPENDENCIES_AVAILABLE:
            return metadata
        
        try:
            audio = FLAC(file_path)
            
            # 获取Vorbis注释
            if "title" in audio:
                metadata["title"] = audio["title"][0]
            
            if "artist" in audio:
                metadata["artist"] = audio["artist"][0]
            
            if "album" in audio:
                metadata["album"] = audio["album"][0]
            
            if "date" in audio:
                metadata["year"] = audio["date"][0]
            
            if "genre" in audio:
                metadata["genre"] = audio["genre"][0]
            
            # 时长
            metadata["duration"] = audio.info.length
            
            return metadata
        except Exception as e:
            print(f"获取FLAC元数据失败: {e}")
            return metadata
    
    def get_lyrics(self, file_path: str) -> Optional[str]:
        """
        获取歌词
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Optional[str]: 歌词文本
        """
        if not DEPENDENCIES_AVAILABLE:
            return None
        
        try:
            # 尝试从Vorbis注释中获取歌词
            audio = FLAC(file_path)
            
            # 检查LYRICS标签
            if "lyrics" in audio:
                return audio["lyrics"][0]
            
            # 尝试查找同名LRC文件
            lrc_path = os.path.splitext(file_path)[0] + ".lrc"
            if os.path.exists(lrc_path):
                with open(lrc_path, 'r', encoding='utf-8') as f:
                    return f.read()
            
            return None
        except Exception as e:
            print(f"获取FLAC歌词失败: {e}")
            return None
