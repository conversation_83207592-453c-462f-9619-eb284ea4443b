#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
歌词功能测试脚本

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.lyrics_format_manager import LyricsFormatManager


def test_lyrics_format_manager():
    """测试歌词格式管理器"""
    print("=" * 50)
    print("测试歌词格式管理器")
    print("=" * 50)
    
    # 创建管理器
    manager = LyricsFormatManager()
    
    # 测试支持的格式
    formats = manager.get_supported_formats()
    print(f"支持的格式: {formats}")
    
    # 测试歌词文件查找
    test_music_file = "大海.mp3"
    if os.path.exists(test_music_file):
        found_files = manager.find_lyrics_files(test_music_file)
        print(f"为 {test_music_file} 找到的歌词文件: {found_files}")
        
        # 如果找到歌词文件，尝试读取
        if found_files:
            lyrics_path, plugin = found_files[0]
            lyrics = manager.read_lyrics(lyrics_path)
            if lyrics:
                print(f"成功读取歌词文件 {lyrics_path}")
                print(f"歌词长度: {len(lyrics)} 字符")
                print(f"歌词前100字符: {lyrics[:100]}...")
            else:
                print(f"无法读取歌词文件 {lyrics_path}")
    
    # 测试歌词写入
    test_lyrics = """从那遥远海边 慢慢消失的你
本来模糊的脸 竟然渐渐清晰
想要说些什么 又不知从何说起
只有把它放在心底"""
    
    test_metadata = {
        "title": "大海",
        "artist": "张雨生",
        "album": "大海",
        "year": "1992"
    }
    
    # 测试不同格式的写入
    for format_type in ["txt", "lrc", "json"]:
        test_file = f"test_lyrics.{format_type}"
        success = manager.write_lyrics(test_file, test_lyrics, format_type, test_metadata)
        if success:
            print(f"成功写入 {format_type} 格式歌词文件: {test_file}")
            
            # 尝试读取回来验证
            read_lyrics = manager.read_lyrics(test_file)
            if read_lyrics:
                print(f"验证读取成功，长度: {len(read_lyrics)} 字符")
            else:
                print(f"验证读取失败")
        else:
            print(f"写入 {format_type} 格式歌词文件失败")


def test_lyrics_search():
    """测试歌词搜索功能"""
    print("\n" + "=" * 50)
    print("测试歌词搜索功能")
    print("=" * 50)
    
    try:
        from gui.widgets.lyrics_search_dialog import LyricsSearchThread
        
        # 创建搜索线程
        search_thread = LyricsSearchThread("张雨生", "大海")
        
        # 执行搜索
        results = search_thread.search_lyrics()
        
        print(f"搜索结果数量: {len(results)}")
        for i, result in enumerate(results):
            print(f"结果 {i+1}:")
            print(f"  来源: {result['source']}")
            print(f"  标题: {result['title']}")
            print(f"  艺术家: {result['artist']}")
            print(f"  歌词长度: {len(result['lyrics'])} 字符")
            print(f"  URL: {result['url']}")
            print()
            
    except ImportError as e:
        print(f"无法导入歌词搜索模块: {e}")
    except Exception as e:
        print(f"歌词搜索测试失败: {e}")


def main():
    """主函数"""
    print("Music Guru 歌词功能测试")
    print("=" * 60)
    
    # 测试歌词格式管理器
    test_lyrics_format_manager()
    
    # 测试歌词搜索功能
    test_lyrics_search()
    
    print("\n" + "=" * 60)
    print("测试完成")


if __name__ == "__main__":
    main()
