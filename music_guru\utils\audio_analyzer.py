#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
音频分析工具

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import numpy as np
from typing import Dict, Tuple, List, Optional

try:
    import librosa
    import librosa.display
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False


class AudioAnalyzer:
    """音频分析工具类"""
    
    def __init__(self):
        """初始化音频分析器"""
        self.audio_data = None
        self.sample_rate = None
        self.duration = 0
    
    def load_audio(self, file_path: str) -> bool:
        """
        加载音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            bool: 是否成功加载
        """
        if not LIBROSA_AVAILABLE:
            print("警告: librosa库未安装，无法进行音频分析")
            return False
        
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 {file_path}")
            return False
        
        try:
            self.audio_data, self.sample_rate = librosa.load(file_path, sr=None)
            self.duration = librosa.get_duration(y=self.audio_data, sr=self.sample_rate)
            return True
        except Exception as e:
            print(f"加载音频文件失败: {e}")
            return False
    
    def analyze_dynamic_range(self) -> float:
        """
        分析动态范围
        
        Returns:
            float: 动态范围（dB）
        """
        if self.audio_data is None:
            return 0
        
        try:
            # 计算RMS能量
            rms = librosa.feature.rms(y=self.audio_data)[0]
            
            # 转换为dB
            rms_db = librosa.amplitude_to_db(rms, ref=np.max)
            
            # 计算动态范围（最大值与最小值之差）
            dynamic_range = np.max(rms_db) - np.min(rms_db)
            
            return dynamic_range
        except Exception as e:
            print(f"分析动态范围失败: {e}")
            return 0
    
    def analyze_noise_floor(self) -> float:
        """
        分析底噪电平
        
        Returns:
            float: 底噪电平（dBFS）
        """
        if self.audio_data is None:
            return 0
        
        try:
            # 找到最安静的部分（假设前5秒或最后5秒可能是静音部分）
            start_segment = self.audio_data[:min(len(self.audio_data), int(5 * self.sample_rate))]
            end_segment = self.audio_data[-min(len(self.audio_data), int(5 * self.sample_rate)):]
            
            # 计算RMS能量
            start_rms = np.sqrt(np.mean(start_segment**2))
            end_rms = np.sqrt(np.mean(end_segment**2))
            
            # 选择较小的值作为底噪
            noise_floor = min(start_rms, end_rms)
            
            # 转换为dBFS
            noise_floor_db = 20 * np.log10(noise_floor) if noise_floor > 0 else -120
            
            return noise_floor_db
        except Exception as e:
            print(f"分析底噪电平失败: {e}")
            return 0
    
    def analyze_intro_hook_timing(self) -> float:
        """
        分析前奏吸引力峰值出现时间
        
        Returns:
            float: 前奏吸引力峰值出现时间（秒）
        """
        if self.audio_data is None:
            return 0
        
        try:
            # 只分析前30秒
            max_time = min(30, self.duration)
            max_samples = int(max_time * self.sample_rate)
            intro_data = self.audio_data[:max_samples]
            
            # 计算短时能量
            frame_length = 2048
            hop_length = 512
            rms = librosa.feature.rms(y=intro_data, frame_length=frame_length, hop_length=hop_length)[0]
            
            # 找到能量峰值
            peak_idx = np.argmax(rms)
            peak_time = librosa.frames_to_time(peak_idx, sr=self.sample_rate, hop_length=hop_length)
            
            return peak_time
        except Exception as e:
            print(f"分析前奏吸引力峰值出现时间失败: {e}")
            return 0
    
    def analyze_chorus_range(self) -> float:
        """
        分析副歌音高范围
        
        Returns:
            float: 副歌最高音与最低音差（音程度数）
        """
        if self.audio_data is None:
            return 0
        
        try:
            # 提取音高
            pitches, magnitudes = librosa.piptrack(y=self.audio_data, sr=self.sample_rate)
            
            # 找到每帧的主要音高
            pitch_values = []
            for t in range(magnitudes.shape[1]):
                index = magnitudes[:, t].argmax()
                pitch = pitches[index, t]
                if pitch > 0:  # 忽略零音高
                    pitch_values.append(pitch)
            
            if not pitch_values:
                return 0
            
            # 转换为MIDI音符编号
            midi_notes = librosa.hz_to_midi(np.array(pitch_values))
            
            # 计算音程范围（半音数量）
            pitch_range = np.max(midi_notes) - np.min(midi_notes)
            
            # 转换为音程度数（12半音 = 1个八度）
            octave_range = pitch_range / 12
            
            return octave_range
        except Exception as e:
            print(f"分析副歌音高范围失败: {e}")
            return 0
