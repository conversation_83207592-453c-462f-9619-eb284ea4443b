#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
音频分析工具

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import numpy as np
from typing import Dict, Tuple, List, Optional

try:
    import librosa
    import librosa.display
    LIBROSA_AVAILABLE = True
except ImportError:
    LIBROSA_AVAILABLE = False


class AudioAnalyzer:
    """音频分析工具类"""

    def __init__(self):
        """初始化音频分析器"""
        self.audio_data = None
        self.sample_rate = None
        self.duration = 0

    def load_audio(self, file_path: str) -> bool:
        """
        加载音频文件

        Args:
            file_path: 音频文件路径

        Returns:
            bool: 是否成功加载
        """
        if not LIBROSA_AVAILABLE:
            print("警告: librosa库未安装，无法进行音频分析")
            return False

        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 {file_path}")
            return False

        try:
            self.audio_data, self.sample_rate = librosa.load(file_path, sr=None)
            self.duration = librosa.get_duration(y=self.audio_data, sr=self.sample_rate)
            return True
        except Exception as e:
            print(f"加载音频文件失败: {e}")
            return False

    def analyze_dynamic_range(self) -> float:
        """
        分析动态范围

        Returns:
            float: 动态范围（dB）
        """
        if self.audio_data is None:
            return 0

        try:
            # 计算RMS能量
            rms = librosa.feature.rms(y=self.audio_data)[0]

            # 转换为dB
            rms_db = librosa.amplitude_to_db(rms, ref=np.max)

            # 计算动态范围（最大值与最小值之差）
            dynamic_range = np.max(rms_db) - np.min(rms_db)

            return dynamic_range
        except Exception as e:
            print(f"分析动态范围失败: {e}")
            return 0

    def analyze_noise_floor(self) -> float:
        """
        分析底噪电平

        Returns:
            float: 底噪电平（dBFS）
        """
        if self.audio_data is None:
            return 0

        try:
            # 找到最安静的部分（假设前5秒或最后5秒可能是静音部分）
            start_segment = self.audio_data[:min(len(self.audio_data), int(5 * self.sample_rate))]
            end_segment = self.audio_data[-min(len(self.audio_data), int(5 * self.sample_rate)):]

            # 计算RMS能量
            start_rms = np.sqrt(np.mean(start_segment**2))
            end_rms = np.sqrt(np.mean(end_segment**2))

            # 选择较小的值作为底噪
            noise_floor = min(start_rms, end_rms)

            # 转换为dBFS
            noise_floor_db = 20 * np.log10(noise_floor) if noise_floor > 0 else -120

            return noise_floor_db
        except Exception as e:
            print(f"分析底噪电平失败: {e}")
            return 0

    def analyze_intro_hook_timing(self) -> float:
        """
        分析前奏吸引力峰值出现时间

        Returns:
            float: 前奏吸引力峰值出现时间（秒）
        """
        if self.audio_data is None:
            return 0

        try:
            # 只分析前30秒
            max_time = min(30, self.duration)
            max_samples = int(max_time * self.sample_rate)
            intro_data = self.audio_data[:max_samples]

            # 计算短时能量
            frame_length = 2048
            hop_length = 512
            rms = librosa.feature.rms(y=intro_data, frame_length=frame_length, hop_length=hop_length)[0]

            # 找到能量峰值
            peak_idx = np.argmax(rms)
            peak_time = librosa.frames_to_time(peak_idx, sr=self.sample_rate, hop_length=hop_length)

            return peak_time
        except Exception as e:
            print(f"分析前奏吸引力峰值出现时间失败: {e}")
            return 0

    def analyze_chorus_range(self) -> float:
        """
        分析副歌音高范围

        Returns:
            float: 副歌最高音与最低音差（音程度数）
        """
        if self.audio_data is None:
            return 0

        try:
            # 提取音高
            pitches, magnitudes = librosa.piptrack(y=self.audio_data, sr=self.sample_rate)

            # 找到每帧的主要音高
            pitch_values = []
            for t in range(magnitudes.shape[1]):
                index = magnitudes[:, t].argmax()
                pitch = pitches[index, t]
                if pitch > 0:  # 忽略零音高
                    pitch_values.append(pitch)

            if not pitch_values:
                return 0

            # 转换为MIDI音符编号
            midi_notes = librosa.hz_to_midi(np.array(pitch_values))

            # 计算音程范围（半音数量）
            pitch_range = np.max(midi_notes) - np.min(midi_notes)

            # 转换为音程度数（12半音 = 1个八度）
            octave_range = pitch_range / 12

            return octave_range
        except Exception as e:
            print(f"分析副歌音高范围失败: {e}")
            return 0

    def analyze_harmony_features(self) -> Dict:
        """
        分析和声进行特征

        Returns:
            Dict: 和声特征字典
        """
        if self.audio_data is None:
            return {}

        try:
            # 使用chromagram分析和弦特征
            chroma = librosa.feature.chroma_stft(y=self.audio_data, sr=self.sample_rate)

            # 计算和弦变化频率（简化版本）
            chroma_diff = np.diff(chroma, axis=1)
            chord_changes = np.sum(np.abs(chroma_diff) > 0.1, axis=1)
            avg_chord_changes = np.mean(chord_changes)

            # 估算公式化套用率（基于和弦进行的重复性）
            # 计算和弦进行的自相关性
            chroma_mean = np.mean(chroma, axis=0)
            autocorr = np.correlate(chroma_mean, chroma_mean, mode='full')
            autocorr = autocorr[autocorr.size // 2:]

            # 寻找周期性模式
            peaks = []
            for i in range(1, min(len(autocorr), 32)):
                if autocorr[i] > 0.7 * autocorr[0]:
                    peaks.append(i)

            # 公式化套用率：有明显周期性模式的程度
            formula_usage = min(len(peaks) * 0.15, 0.8)  # 0-0.8范围

            # 调式转换频率：基于色度变化的剧烈程度
            key_changes = int(avg_chord_changes / 10)  # 简化计算

            # 替代和弦使用率：基于非常规和弦的使用
            # 计算主要调性
            key_profile = np.mean(chroma, axis=1)
            major_profile = np.array([1, 0, 1, 0, 1, 1, 0, 1, 0, 1, 0, 1])  # C大调模板

            # 计算与标准大调的相似度
            similarity = np.corrcoef(key_profile, major_profile)[0, 1]
            alt_chord_ratio = max(0, 1 - similarity) * 0.5  # 0-0.5范围

            return {
                "formula_usage": formula_usage,
                "key_changes": key_changes,
                "alt_chord_ratio": alt_chord_ratio
            }
        except Exception as e:
            print(f"和声分析失败: {e}")
            return {}

    def analyze_melody_features(self) -> Dict:
        """
        分析旋律结构特征

        Returns:
            Dict: 旋律特征字典
        """
        if self.audio_data is None:
            return {}

        try:
            # 提取音高
            pitches, magnitudes = librosa.piptrack(y=self.audio_data, sr=self.sample_rate)

            # 找到每帧的主要音高
            pitch_values = []
            for t in range(magnitudes.shape[1]):
                index = magnitudes[:, t].argmax()
                pitch = pitches[index, t]
                if pitch > 0:
                    pitch_values.append(pitch)

            if not pitch_values:
                return {}

            # 转换为MIDI音符编号
            midi_notes = librosa.hz_to_midi(np.array(pitch_values))

            # 计算音程跨度
            interval_range = np.max(midi_notes) - np.min(midi_notes)

            # 计算Hook密度（旋律重复模式的密度）
            # 简化版本：计算旋律片段的重复频率
            segment_length = min(len(midi_notes) // 10, 20)
            if segment_length > 5:
                segments = []
                for i in range(0, len(midi_notes) - segment_length, segment_length):
                    segments.append(midi_notes[i:i+segment_length])

                # 计算相似片段的数量
                similar_count = 0
                for i in range(len(segments)):
                    for j in range(i+1, len(segments)):
                        if len(segments[i]) == len(segments[j]):
                            correlation = np.corrcoef(segments[i], segments[j])[0, 1]
                            if not np.isnan(correlation) and correlation > 0.8:
                                similar_count += 1

                # Hook密度：相似片段密度转换为时间间隔
                if similar_count > 0:
                    hook_density = self.duration / (similar_count + 1)
                else:
                    hook_density = self.duration
            else:
                hook_density = self.duration

            # 逆向旋律检测（简化版本）
            # 检测是否有明显的下行旋律模式
            pitch_diff = np.diff(midi_notes)
            descending_ratio = np.sum(pitch_diff < -2) / len(pitch_diff) if len(pitch_diff) > 0 else 0
            reverse_melody = descending_ratio > 0.3

            return {
                "interval_range": interval_range,
                "hook_density": hook_density,
                "reverse_melody": reverse_melody
            }
        except Exception as e:
            print(f"旋律分析失败: {e}")
            return {}

    def analyze_streaming_features(self) -> Dict:
        """
        分析流媒体适配特征

        Returns:
            Dict: 流媒体特征字典
        """
        if self.audio_data is None:
            return {}

        try:
            # 分析移动端优化（基于频率分布）
            # 计算频谱
            stft = librosa.stft(self.audio_data)
            magnitude = np.abs(stft)
            freqs = librosa.fft_frequencies(sr=self.sample_rate)

            # 计算低频、中频、高频的能量分布
            low_freq_mask = freqs < 250
            mid_freq_mask = (freqs >= 250) & (freqs < 4000)
            high_freq_mask = freqs >= 4000

            low_energy = np.mean(magnitude[low_freq_mask, :])
            mid_energy = np.mean(magnitude[mid_freq_mask, :])
            high_energy = np.mean(magnitude[high_freq_mask, :])

            total_energy = low_energy + mid_energy + high_energy

            if total_energy > 0:
                low_ratio = low_energy / total_energy
                mid_ratio = mid_energy / total_energy
                high_ratio = high_energy / total_energy

                # 移动端优化：中频能量占主导，低频和高频适中
                # EBU R128标准倾向于平衡的频率分布
                mobile_optimization = (0.3 < mid_ratio < 0.6) and (0.2 < low_ratio < 0.4) and (high_ratio < 0.3)
            else:
                mobile_optimization = False

            return {
                "mobile_optimization": mobile_optimization
            }
        except Exception as e:
            print(f"流媒体分析失败: {e}")
            return {}