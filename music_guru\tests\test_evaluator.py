#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
评价引擎测试

MIT License
Copyright (c) 2025 Music Guru
"""

import unittest
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from core.plugin_manager import PluginManager, Plugin
from core.evaluator import Evaluator
from core.data_model import MusicData
from config.settings import Settings


# 创建测试插件
class TestPlugin(Plugin):
    """测试插件"""
    
    def __init__(self, name, dimension, score):
        super().__init__()
        self.name = name
        self.dimension = dimension
        self.category = "test"
        self.test_score = score
    
    def evaluate(self, data):
        """返回固定分数"""
        return {
            "score": self.test_score,
            "details": {"test": f"测试分数: {self.test_score}"}
        }


class TestEvaluator(unittest.TestCase):
    """评价引擎测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.plugin_manager = PluginManager()
        
        # 添加测试插件
        self.plugin_manager.plugins = {
            "technical.test1": TestPlugin("技术测试1", "technical", 80),
            "technical.test2": TestPlugin("技术测试2", "technical", 70),
            "emotional.test1": TestPlugin("情感测试1", "emotional", 90),
            "market.test1": TestPlugin("市场测试1", "market", 60),
            "cultural.test1": TestPlugin("文化测试1", "cultural", 85),
            "production.test1": TestPlugin("制作测试1", "production", 75)
        }
        
        # 创建设置
        self.settings = Settings()
        
        # 创建评价引擎
        self.evaluator = Evaluator(self.plugin_manager, self.settings)
        
        # 创建测试数据
        self.test_data = MusicData()
    
    def test_evaluate(self):
        """测试评价功能"""
        # 执行评价
        results = self.evaluator.evaluate(self.test_data)
        
        # 检查结果数量
        self.assertEqual(len(results), 6)
        
        # 检查各插件的评价结果
        self.assertEqual(results["technical.test1"].score, 80)
        self.assertEqual(results["emotional.test1"].score, 90)
        self.assertEqual(results["market.test1"].score, 60)
    
    def test_dimension_scores(self):
        """测试维度得分计算"""
        # 执行评价
        self.evaluator.evaluate(self.test_data)
        
        # 计算维度得分
        dimension_scores = self.evaluator.calculate_dimension_scores()
        
        # 检查维度得分
        self.assertEqual(dimension_scores["technical"], 75)  # (80 + 70) / 2
        self.assertEqual(dimension_scores["emotional"], 90)
        self.assertEqual(dimension_scores["market"], 60)
        self.assertEqual(dimension_scores["cultural"], 85)
        self.assertEqual(dimension_scores["production"], 75)
    
    def test_total_score(self):
        """测试总分计算"""
        # 执行评价
        self.evaluator.evaluate(self.test_data)
        
        # 计算总分
        total_score = self.evaluator.calculate_total_score()
        
        # 手动计算加权平均
        expected_score = (
            75 * 0.30 +  # 技术创作
            90 * 0.25 +  # 情感表达
            60 * 0.20 +  # 市场传播
            85 * 0.15 +  # 文化创新
            75 * 0.10    # 制作工程
        )
        
        # 检查总分
        self.assertAlmostEqual(total_score, expected_score, places=2)
    
    def test_cultural_market_boost(self):
        """测试文化创新高分时市场传播权重提升"""
        # 修改文化创新得分为90分（大于80分）
        self.plugin_manager.plugins["cultural.test1"] = TestPlugin("文化测试1", "cultural", 90)
        
        # 执行评价
        self.evaluator.evaluate(self.test_data)
        
        # 计算总分
        total_score = self.evaluator.calculate_total_score()
        
        # 手动计算加权平均（市场传播权重提升0.05）
        expected_score = (
            75 * 0.30 +  # 技术创作
            90 * 0.25 +  # 情感表达
            60 * 0.25 +  # 市场传播（0.20 + 0.05）
            90 * 0.15 +  # 文化创新
            75 * 0.10    # 制作工程
        ) / 1.05  # 总权重变为1.05
        
        # 检查总分
        self.assertAlmostEqual(total_score, expected_score, places=2)


if __name__ == '__main__':
    unittest.main()
