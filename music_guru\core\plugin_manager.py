#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
插件管理器

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import importlib
import inspect
from typing import Dict, List, Type

# 插件基类
class Plugin:
    """插件基类"""
    
    def __init__(self):
        self.name = "基础插件"
        self.description = "插件基类"
        self.dimension = "base"  # 维度类型
        self.category = "base"   # 分类
        self.parameters = {}     # 参数列表
        self.enabled = True      # 是否启用
    
    def evaluate(self, data) -> dict:
        """
        评价方法，需要子类实现
        
        Args:
            data: 评价数据
            
        Returns:
            dict: 评价结果，包含分数和详细信息
        """
        raise NotImplementedError("子类必须实现evaluate方法")
    
    def get_parameters(self) -> dict:
        """获取参数列表"""
        return self.parameters
    
    def set_parameter(self, key, value):
        """设置参数值"""
        if key in self.parameters:
            self.parameters[key] = value
            return True
        return False


class PluginManager:
    """插件管理器"""
    
    def __init__(self):
        """初始化插件管理器"""
        self.plugins: Dict[str, Plugin] = {}  # 所有插件
        self.dimensions = {
            "technical": "技术创作",
            "emotional": "情感表达",
            "market": "市场传播",
            "cultural": "文化创新",
            "production": "制作工程"
        }
        
        # 插件目录
        self.plugin_dirs = {
            "technical": os.path.join(os.path.dirname(os.path.dirname(__file__)), "plugins", "technical"),
            "emotional": os.path.join(os.path.dirname(os.path.dirname(__file__)), "plugins", "emotional"),
            "market": os.path.join(os.path.dirname(os.path.dirname(__file__)), "plugins", "market"),
            "cultural": os.path.join(os.path.dirname(os.path.dirname(__file__)), "plugins", "cultural"),
            "production": os.path.join(os.path.dirname(os.path.dirname(__file__)), "plugins", "production")
        }
    
    def load_all_plugins(self):
        """加载所有插件"""
        for dimension, plugin_dir in self.plugin_dirs.items():
            self.load_plugins_from_directory(dimension, plugin_dir)
    
    def load_plugins_from_directory(self, dimension: str, directory: str):
        """从目录加载插件"""
        if not os.path.exists(directory):
            print(f"插件目录不存在: {directory}")
            return
        
        # 获取目录中的所有Python文件
        for filename in os.listdir(directory):
            if filename.endswith('.py') and not filename.startswith('__'):
                module_name = filename[:-3]  # 去掉.py后缀
                
                try:
                    # 构建模块路径
                    module_path = f"plugins.{dimension}.{module_name}"
                    
                    # 导入模块
                    module = importlib.import_module(module_path)
                    
                    # 查找模块中的插件类
                    for name, obj in inspect.getmembers(module):
                        if (inspect.isclass(obj) and 
                            issubclass(obj, Plugin) and 
                            obj is not Plugin):
                            
                            # 创建插件实例
                            plugin = obj()
                            plugin_id = f"{dimension}.{module_name}.{name}"
                            
                            # 添加到插件列表
                            self.plugins[plugin_id] = plugin
                            print(f"已加载插件: {plugin.name} ({plugin_id})")
                
                except Exception as e:
                    print(f"加载插件失败 {module_name}: {e}")
    
    def get_plugins(self, dimension=None) -> Dict[str, Plugin]:
        """获取插件列表"""
        if dimension:
            return {pid: p for pid, p in self.plugins.items() if p.dimension == dimension}
        return self.plugins
    
    def get_plugin(self, plugin_id) -> Plugin:
        """获取指定插件"""
        return self.plugins.get(plugin_id)
    
    def get_enabled_plugins(self) -> Dict[str, Plugin]:
        """获取已启用的插件"""
        return {pid: p for pid, p in self.plugins.items() if p.enabled}
    
    def enable_plugin(self, plugin_id, enabled=True):
        """启用或禁用插件"""
        if plugin_id in self.plugins:
            self.plugins[plugin_id].enabled = enabled
            return True
        return False
    
    def get_dimensions(self) -> Dict[str, str]:
        """获取所有维度"""
        return self.dimensions
