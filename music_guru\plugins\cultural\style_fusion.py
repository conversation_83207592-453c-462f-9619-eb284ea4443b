#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
风格融合插件

MIT License
Copyright (c) 2025 Music Guru
"""

from core.plugin_manager import Plugin


class StyleFusionPlugin(Plugin):
    """风格融合插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "风格融合"
        self.description = "评价风格融合（跨流派元素占比）"
        self.dimension = "cultural"
        self.category = "style_fusion"
        
        # 参数设置
        self.parameters = {
            "cross_genre_ratio": 0,  # 跨流派元素占比
            "subculture_density": 0,  # 亚文化符号密度
            "ai_content_originality": False  # AI生成内容原创性认证
        }
    
    def evaluate(self, data):
        """
        评价风格融合
        
        评分标准：
        - 跨流派元素占比＞40%（+4分）
        - 亚文化符号密度＞5个/分钟（+3分）
        - AI生成内容原创性认证（+2分）
        
        满分：9分
        """
        score = 0
        details = {}
        
        # 获取参数
        cross_genre_ratio = data.cultural_data.get("style_fusion", {}).get("cross_genre_ratio", 0)
        subculture_density = data.cultural_data.get("style_fusion", {}).get("subculture_density", 0)
        ai_content_originality = data.cultural_data.get("style_fusion", {}).get("ai_content_originality", False)
        
        # 评分计算
        # 1. 跨流派元素占比＞40%（+4分）
        if cross_genre_ratio > 0.4:
            score += 4
            details["cross_genre_ratio"] = f"跨流派元素占比为{cross_genre_ratio*100:.1f}%，大于40%，+4分"
        else:
            details["cross_genre_ratio"] = f"跨流派元素占比为{cross_genre_ratio*100:.1f}%，不符合要求，+0分"
        
        # 2. 亚文化符号密度＞5个/分钟（+3分）
        if subculture_density > 5:
            score += 3
            details["subculture_density"] = f"亚文化符号密度为{subculture_density}个/分钟，大于5个/分钟，+3分"
        else:
            details["subculture_density"] = f"亚文化符号密度为{subculture_density}个/分钟，不符合要求，+0分"
        
        # 3. AI生成内容原创性认证（+2分）
        if ai_content_originality:
            score += 2
            details["ai_content_originality"] = "AI生成内容原创性认证通过，+2分"
        else:
            details["ai_content_originality"] = "AI生成内容原创性认证未通过，+0分"
        
        # 标准化分数到100分制
        normalized_score = (score / 9) * 100
        
        return {
            "score": normalized_score,
            "details": details
        }
