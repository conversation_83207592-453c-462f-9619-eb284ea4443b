#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
录音质量插件

MIT License
Copyright (c) 2025 Music Guru
"""

from core.plugin_manager import Plugin


class RecordingQualityPlugin(Plugin):
    """录音质量插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "录音质量"
        self.description = "评价录音质量（动态范围/底噪电平）"
        self.dimension = "production"
        self.category = "recording_quality"
        
        # 参数设置
        self.parameters = {
            "dynamic_range": 0,  # 动态范围
            "noise_floor": 0,  # 底噪电平
            "apple_masters": False  # 符合Apple Digital Masters认证
        }
    
    def evaluate(self, data):
        """
        评价录音质量
        
        评分标准：
        - 动态范围＞12dB（+3分）
        - 底噪电平＜-90dBFS（+2分）
        - 符合Apple Digital Masters认证（+1分）
        
        满分：6分
        """
        score = 0
        details = {}
        
        # 获取参数
        dynamic_range = data.production_data.get("recording_quality", {}).get("dynamic_range", 0)
        noise_floor = data.production_data.get("recording_quality", {}).get("noise_floor", 0)
        apple_masters = data.production_data.get("recording_quality", {}).get("apple_masters", False)
        
        # 评分计算
        # 1. 动态范围＞12dB（+3分）
        if dynamic_range > 12:
            score += 3
            details["dynamic_range"] = f"动态范围为{dynamic_range}dB，大于12dB，+3分"
        else:
            details["dynamic_range"] = f"动态范围为{dynamic_range}dB，不符合要求，+0分"
        
        # 2. 底噪电平＜-90dBFS（+2分）
        if noise_floor < -90:
            score += 2
            details["noise_floor"] = f"底噪电平为{noise_floor}dBFS，小于-90dBFS，+2分"
        else:
            details["noise_floor"] = f"底噪电平为{noise_floor}dBFS，不符合要求，+0分"
        
        # 3. 符合Apple Digital Masters认证（+1分）
        if apple_masters:
            score += 1
            details["apple_masters"] = "符合Apple Digital Masters认证，+1分"
        else:
            details["apple_masters"] = "不符合Apple Digital Masters认证，+0分"
        
        # 标准化分数到100分制
        normalized_score = (score / 6) * 100
        
        return {
            "score": normalized_score,
            "details": details
        }
