#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复效果的脚本
"""

import os
import sys
import logging

print("开始测试修复效果...")

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.batch_processor import BatchProcessor
from core.evaluator import Evaluator

def test_single_file():
    """测试单个文件的评价差异化"""

    # 设置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    # 创建批量处理器
    processor = BatchProcessor()

    # 测试文件列表（选择几个不同的文件）
    test_files = [
        "03.最动听.[www.8WQ.com] - 房祖名.wma",
        "04.吻我的样子[yyrl.com] - 李冰冰.mp3",
        "alexclare-too close.mp3",
        "<PERSON>-All Around The World.wma"
    ]

    results = {}

    for filename in test_files:
        # 构建完整路径
        file_path = os.path.join("test_music", filename)

        if os.path.exists(file_path):
            print(f"\n{'='*60}")
            print(f"测试文件: {filename}")
            print(f"{'='*60}")

            try:
                # 处理单个文件
                result = processor.process_file(file_path)

                if result and result.get("evaluation"):
                    evaluation = result["evaluation"]
                    total_score = evaluation.get("total_score", 0)
                    dimension_scores = evaluation.get("dimension_scores", {})

                    print(f"总分: {total_score:.2f}")
                    print(f"各维度得分:")
                    for dim, score in dimension_scores.items():
                        print(f"  {dim}: {score:.2f}")

                    results[filename] = {
                        "total_score": total_score,
                        "dimension_scores": dimension_scores
                    }
                else:
                    print(f"处理失败: {filename}")

            except Exception as e:
                print(f"处理出错: {e}")
        else:
            print(f"文件不存在: {file_path}")

    # 分析结果差异
    print(f"\n{'='*60}")
    print("结果差异分析")
    print(f"{'='*60}")

    if len(results) > 1:
        scores = [r["total_score"] for r in results.values()]
        max_score = max(scores)
        min_score = min(scores)
        avg_score = sum(scores) / len(scores)

        print(f"总分统计:")
        print(f"  最高分: {max_score:.2f}")
        print(f"  最低分: {min_score:.2f}")
        print(f"  平均分: {avg_score:.2f}")
        print(f"  分数差异: {max_score - min_score:.2f}")

        if max_score - min_score > 0.1:
            print("✅ 检测到评分差异化，修复成功！")
        else:
            print("❌ 评分仍然相同，需要进一步修复")

        # 分析各维度差异
        print(f"\n各维度差异分析:")
        dimensions = ["technical", "emotional", "market", "cultural", "production"]

        for dim in dimensions:
            dim_scores = []
            for result in results.values():
                if dim in result["dimension_scores"]:
                    dim_scores.append(result["dimension_scores"][dim])

            if dim_scores:
                max_dim = max(dim_scores)
                min_dim = min(dim_scores)
                print(f"  {dim}: 最高{max_dim:.2f}, 最低{min_dim:.2f}, 差异{max_dim-min_dim:.2f}")
    else:
        print("测试文件不足，无法分析差异")

def test_audio_analysis():
    """测试音频分析功能"""
    print(f"\n{'='*60}")
    print("测试音频分析功能")
    print(f"{'='*60}")

    from utils.audio_analyzer import AudioAnalyzer
    from utils.audio_format_plugin import AudioFormatManager

    # 测试文件
    test_file = "test_music/03.最动听.[www.8WQ.com] - 房祖名.wma"

    if os.path.exists(test_file):
        # 加载音频
        manager = AudioFormatManager()
        plugin = manager.get_plugin(test_file)

        if plugin:
            audio_data, sample_rate = plugin.load_audio(test_file)

            if audio_data is not None:
                print(f"✅ 音频加载成功: 采样率={sample_rate}, 长度={len(audio_data)}")

                # 测试音频分析
                analyzer = AudioAnalyzer()
                analyzer.audio_data = audio_data
                analyzer.sample_rate = sample_rate
                analyzer.duration = len(audio_data) / sample_rate

                # 测试各种分析功能
                try:
                    dr = analyzer.analyze_dynamic_range()
                    print(f"动态范围: {dr:.2f}dB")

                    nf = analyzer.analyze_noise_floor()
                    print(f"底噪电平: {nf:.2f}dBFS")

                    iht = analyzer.analyze_intro_hook_timing()
                    print(f"前奏峰值时间: {iht:.2f}秒")

                    cr = analyzer.analyze_chorus_range()
                    print(f"副歌音域: {cr:.2f}度")

                    # 测试新增的分析功能
                    harmony = analyzer.analyze_harmony_features()
                    if harmony:
                        print(f"和声特征: {harmony}")

                    melody = analyzer.analyze_melody_features()
                    if melody:
                        print(f"旋律特征: {melody}")

                    streaming = analyzer.analyze_streaming_features()
                    if streaming:
                        print(f"流媒体特征: {streaming}")

                    print("✅ 音频分析功能正常")

                except Exception as e:
                    print(f"❌ 音频分析失败: {e}")
            else:
                print("❌ 音频加载失败")
        else:
            print("❌ 无法获取音频插件")
    else:
        print(f"❌ 测试文件不存在: {test_file}")

def test_lyrics_analysis():
    """测试歌词分析功能"""
    print(f"\n{'='*60}")
    print("测试歌词分析功能")
    print(f"{'='*60}")

    from plugins.emotional.lyrics_analysis import LyricsAnalysisPlugin
    from core.data_model import MusicData

    # 创建测试歌词
    test_lyrics = [
        """
        你是我心中的太阳
        照亮我前进的方向
        像春风一样温暖
        如星光一样闪亮
        """,
        """
        Love is like a butterfly
        Flying high up in the sky
        Beautiful and free
        Dancing gracefully
        """,
        """
        雨滴敲打着窗台
        思念如潮水般涌来
        回忆里的你
        依然那么美丽
        """
    ]

    plugin = LyricsAnalysisPlugin()

    for i, lyrics in enumerate(test_lyrics):
        print(f"\n测试歌词 {i+1}:")
        print(f"内容: {lyrics.strip()[:50]}...")

        # 创建音乐数据
        data = MusicData()
        data.title = f"测试歌曲{i+1}"
        data.lyrics = lyrics.strip()

        try:
            result = plugin.evaluate(data)
            score = result.get("score", 0)
            details = result.get("details", {})

            print(f"评分: {score:.2f}")
            for key, detail in details.items():
                print(f"  {key}: {detail}")

        except Exception as e:
            print(f"❌ 歌词分析失败: {e}")

if __name__ == "__main__":
    print("开始测试修复效果...")

    # 测试音频分析
    test_audio_analysis()

    # 测试歌词分析
    test_lyrics_analysis()

    # 测试单文件评价差异化
    test_single_file()

    print(f"\n{'='*60}")
    print("测试完成")
    print(f"{'='*60}")
