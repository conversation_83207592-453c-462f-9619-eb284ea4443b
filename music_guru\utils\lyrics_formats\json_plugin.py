#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
JSON歌词格式插件

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import json
from typing import Dict, Optional
from utils.lyrics_format_manager import LyricsFormatPlugin


class JsonLyricsPlugin(LyricsFormatPlugin):
    """JSON歌词格式插件"""
    
    def __init__(self):
        super().__init__()
        self.name = "JSON结构化歌词"
        self.description = "支持JSON格式的结构化歌词文件"
        self.file_extensions = [".json", ".lyrics"]
    
    def can_handle(self, file_path: str) -> bool:
        """检查是否能处理指定文件"""
        ext = os.path.splitext(file_path)[1].lower()
        return ext in self.file_extensions
    
    def read_lyrics(self, file_path: str) -> Optional[str]:
        """读取JSON歌词文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 提取歌词内容
            lyrics = self._extract_lyrics_from_json(data)
            if lyrics:
                self.logger.info(f"成功读取JSON歌词文件: {file_path}")
                return lyrics
            else:
                self.logger.warning(f"JSON文件中未找到歌词内容: {file_path}")
                return None
                
        except json.JSONDecodeError as e:
            self.logger.error(f"JSON格式错误: {e}")
            return None
        except Exception as e:
            self.logger.error(f"读取JSON歌词文件失败: {e}")
            return None
    
    def _extract_lyrics_from_json(self, data: Dict) -> Optional[str]:
        """从JSON数据中提取歌词"""
        # 支持多种JSON结构
        
        # 格式1: {"lyrics": "歌词内容"}
        if "lyrics" in data and isinstance(data["lyrics"], str):
            return data["lyrics"]
        
        # 格式2: {"content": "歌词内容"}
        if "content" in data and isinstance(data["content"], str):
            return data["content"]
        
        # 格式3: {"text": "歌词内容"}
        if "text" in data and isinstance(data["text"], str):
            return data["text"]
        
        # 格式4: {"lines": ["第一行", "第二行", ...]}
        if "lines" in data and isinstance(data["lines"], list):
            return '\n'.join(str(line) for line in data["lines"])
        
        # 格式5: {"verses": [{"text": "..."}, ...]}
        if "verses" in data and isinstance(data["verses"], list):
            lines = []
            for verse in data["verses"]:
                if isinstance(verse, dict) and "text" in verse:
                    lines.append(str(verse["text"]))
                elif isinstance(verse, str):
                    lines.append(verse)
            if lines:
                return '\n'.join(lines)
        
        # 格式6: {"sections": [{"type": "verse", "content": "..."}, ...]}
        if "sections" in data and isinstance(data["sections"], list):
            lines = []
            for section in data["sections"]:
                if isinstance(section, dict) and "content" in section:
                    lines.append(str(section["content"]))
            if lines:
                return '\n'.join(lines)
        
        return None
    
    def write_lyrics(self, file_path: str, lyrics: str, metadata: Dict = None) -> bool:
        """写入JSON歌词文件"""
        try:
            data = {
                "lyrics": lyrics,
                "lines": lyrics.split('\n'),
                "metadata": metadata or {}
            }
            
            # 添加时间戳
            import datetime
            data["created_at"] = datetime.datetime.now().isoformat()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"成功写入JSON歌词文件: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"写入JSON歌词文件失败: {e}")
            return False
    
    def get_metadata(self, file_path: str) -> Dict:
        """获取JSON歌词文件的元数据"""
        metadata = {}
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 提取元数据
            if "metadata" in data and isinstance(data["metadata"], dict):
                metadata.update(data["metadata"])
            
            # 提取其他可能的元数据字段
            meta_fields = ["title", "artist", "album", "year", "genre", "created_at", "updated_at"]
            for field in meta_fields:
                if field in data:
                    metadata[field] = data[field]
                    
        except Exception as e:
            self.logger.error(f"获取JSON歌词元数据失败: {e}")
            
        return metadata
