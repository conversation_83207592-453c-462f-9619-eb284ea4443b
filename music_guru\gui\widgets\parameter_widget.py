#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
参数控件

MIT License
Copyright (c) 2025 Music Guru
"""

from PyQt6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QSlider, QSpinBox, QDoubleSpinBox, QCheckBox,
                            QComboBox, QGroupBox)
from PyQt6.QtCore import Qt, pyqtSignal


class ParameterWidget(QWidget):
    """参数控件"""
    
    # 参数值变化信号
    valueChanged = pyqtSignal(str, object)
    
    def __init__(self, param_name, param_type, param_value, param_range=None, parent=None):
        super().__init__(parent)
        
        self.param_name = param_name
        self.param_type = param_type
        self.param_value = param_value
        self.param_range = param_range or {}
        
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QHBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 参数名称标签
        name_label = QLabel(self.param_name)
        name_label.setMinimumWidth(150)
        layout.addWidget(name_label)
        
        # 根据参数类型创建不同的控件
        if self.param_type == bool:
            self.value_widget = QCheckBox()
            self.value_widget.setChecked(self.param_value)
            self.value_widget.stateChanged.connect(self.on_bool_changed)
        
        elif self.param_type == int:
            min_val = self.param_range.get('min', 0)
            max_val = self.param_range.get('max', 100)
            
            self.value_widget = QSpinBox()
            self.value_widget.setRange(min_val, max_val)
            self.value_widget.setValue(self.param_value)
            self.value_widget.valueChanged.connect(self.on_int_changed)
            
            # 如果有步长
            if 'step' in self.param_range:
                self.value_widget.setSingleStep(self.param_range['step'])
        
        elif self.param_type == float:
            min_val = self.param_range.get('min', 0.0)
            max_val = self.param_range.get('max', 1.0)
            
            self.value_widget = QDoubleSpinBox()
            self.value_widget.setRange(min_val, max_val)
            self.value_widget.setValue(self.param_value)
            self.value_widget.valueChanged.connect(self.on_float_changed)
            
            # 如果有步长和小数位数
            if 'step' in self.param_range:
                self.value_widget.setSingleStep(self.param_range['step'])
            
            if 'decimals' in self.param_range:
                self.value_widget.setDecimals(self.param_range['decimals'])
            else:
                self.value_widget.setDecimals(2)
        
        elif self.param_type == str and 'options' in self.param_range:
            self.value_widget = QComboBox()
            self.value_widget.addItems(self.param_range['options'])
            
            # 设置当前值
            index = self.value_widget.findText(self.param_value)
            if index >= 0:
                self.value_widget.setCurrentIndex(index)
            
            self.value_widget.currentTextChanged.connect(self.on_str_changed)
        
        else:  # 默认为标签
            self.value_widget = QLabel(str(self.param_value))
        
        layout.addWidget(self.value_widget)
    
    def on_bool_changed(self, state):
        """布尔值变化处理"""
        value = state == Qt.CheckState.Checked.value
        self.param_value = value
        self.valueChanged.emit(self.param_name, value)
    
    def on_int_changed(self, value):
        """整数值变化处理"""
        self.param_value = value
        self.valueChanged.emit(self.param_name, value)
    
    def on_float_changed(self, value):
        """浮点值变化处理"""
        self.param_value = value
        self.valueChanged.emit(self.param_name, value)
    
    def on_str_changed(self, value):
        """字符串值变化处理"""
        self.param_value = value
        self.valueChanged.emit(self.param_name, value)
    
    def get_value(self):
        """获取参数值"""
        return self.param_value
    
    def set_value(self, value):
        """设置参数值"""
        self.param_value = value
        
        # 更新控件值
        if self.param_type == bool and isinstance(self.value_widget, QCheckBox):
            self.value_widget.setChecked(value)
        
        elif self.param_type == int and isinstance(self.value_widget, QSpinBox):
            self.value_widget.setValue(value)
        
        elif self.param_type == float and isinstance(self.value_widget, QDoubleSpinBox):
            self.value_widget.setValue(value)
        
        elif self.param_type == str and isinstance(self.value_widget, QComboBox):
            index = self.value_widget.findText(value)
            if index >= 0:
                self.value_widget.setCurrentIndex(index)
        
        elif isinstance(self.value_widget, QLabel):
            self.value_widget.setText(str(value))
