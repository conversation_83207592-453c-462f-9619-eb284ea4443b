#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
简化版主程序入口

MIT License
Copyright (c) 2025 Music Guru
"""

import sys
import os
import logging
from PyQt6.QtWidgets import QApplication, QMainWindow, QLabel, QVBoxLayout, QWidget

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('music_guru.log', encoding='utf-8')
    ]
)

logger = logging.getLogger("MainSimple")

class SimpleMainWindow(QMainWindow):
    """简化版主窗口"""
    
    def __init__(self):
        super().__init__()
        
        self.setWindowTitle("Music Guru - 简化版")
        self.setMinimumSize(800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 添加标签
        label = QLabel("Music Guru - 流行音乐评价软件")
        label.setStyleSheet("font-size: 24px; font-weight: bold;")
        layout.addWidget(label)
        
        # 添加说明
        info_label = QLabel("这是一个简化版的 Music Guru，用于测试基本功能。")
        layout.addWidget(info_label)
        
        logger.info("简化版主窗口已创建")

def main():
    """主程序入口函数"""
    logger.info("程序启动")
    
    # 初始化应用
    app = QApplication(sys.argv)
    app.setApplicationName("Music Guru")
    app.setApplicationVersion("1.0.0")
    
    # 创建主窗口
    main_window = SimpleMainWindow()
    main_window.show()
    
    logger.info("主窗口已显示")
    
    # 运行应用
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
