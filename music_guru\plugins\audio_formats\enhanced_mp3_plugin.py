#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
增强版MP3格式插件 - 支持各种版本的MP3格式

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import io
import struct
import logging
from typing import Tuple, Optional, Dict, Any, List

from utils.audio_format_plugin import AudioFormatPlugin

# 尝试导入各种MP3处理库
backends = {}

try:
    import mutagen
    from mutagen.mp3 import MP3
    from mutagen.id3 import ID3
    backends["mutagen"] = True
except ImportError:
    backends["mutagen"] = False

try:
    import eyed3
    backends["eyed3"] = True
except ImportError:
    backends["eyed3"] = False

try:
    import pydub
    backends["pydub"] = True
except ImportError:
    backends["pydub"] = False

try:
    import librosa
    backends["librosa"] = True
except ImportError:
    backends["librosa"] = False

try:
    import miniaudio
    backends["miniaudio"] = True
except ImportError:
    backends["miniaudio"] = False

try:
    import tinytag
    backends["tinytag"] = True
except ImportError:
    backends["tinytag"] = False


class EnhancedMP3Plugin(AudioFormatPlugin):
    """增强版MP3格式插件 - 支持各种版本的MP3格式"""
    
    def __init__(self):
        """初始化增强版MP3格式插件"""
        super().__init__()
        self.name = "增强版MP3格式"
        self.description = "支持各种版本的MP3音频格式，包括老版本和最新版本"
        self.extensions = ["mp3"]
        self.mime_types = ["audio/mpeg", "audio/mp3", "audio/mpeg3", "audio/x-mpeg-3"]
        self.versions = ["MPEG 1 Layer I", "MPEG 1 Layer II", "MPEG 1 Layer III", 
                         "MPEG 2 Layer I", "MPEG 2 Layer II", "MPEG 2 Layer III",
                         "MPEG 2.5 Layer I", "MPEG 2.5 Layer II", "MPEG 2.5 Layer III"]
        self.priority = 100  # 高优先级
    
    def _load_backends(self) -> Dict[str, bool]:
        """加载后端库"""
        return backends
    
    def can_handle(self, file_path: str) -> bool:
        """
        检查是否可以处理指定文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            bool: 是否可以处理
        """
        if not os.path.exists(file_path):
            return False
        
        ext = self.get_file_extension(file_path)
        if ext not in self.extensions:
            return False
        
        # 尝试使用多种方法检测MP3格式
        try:
            # 方法1: 检查文件头
            with open(file_path, 'rb') as f:
                header = f.read(10)
                # 检查MP3文件头标识
                if header.startswith(b'\xFF\xFB') or header.startswith(b'ID3'):
                    return True
            
            # 方法2: 使用mutagen
            if backends["mutagen"]:
                try:
                    MP3(file_path)
                    return True
                except:
                    pass
            
            # 方法3: 使用eyed3
            if backends["eyed3"]:
                try:
                    if eyed3.load(file_path):
                        return True
                except:
                    pass
            
            # 方法4: 使用tinytag
            if backends["tinytag"]:
                try:
                    tag = tinytag.TinyTag.get(file_path)
                    if tag.bitrate > 0:
                        return True
                except:
                    pass
            
            # 方法5: 使用pydub
            if backends["pydub"]:
                try:
                    pydub.AudioSegment.from_mp3(file_path)
                    return True
                except:
                    pass
            
            # 方法6: 使用miniaudio
            if backends["miniaudio"]:
                try:
                    miniaudio.decode_file(file_path)
                    return True
                except:
                    pass
            
            # 方法7: 使用librosa
            if backends["librosa"]:
                try:
                    librosa.load(file_path, sr=None, mono=True, offset=0.0, duration=0.1)
                    return True
                except:
                    pass
            
            return False
        except Exception as e:
            self.logger.error(f"检查MP3文件失败: {e}")
            return False
    
    def get_version(self, file_path: str) -> str:
        """
        获取MP3文件版本
        
        Args:
            file_path: 文件路径
            
        Returns:
            str: 文件版本
        """
        try:
            # 尝试读取MP3头信息
            with open(file_path, 'rb') as f:
                # 跳过ID3标签
                header = f.read(10)
                if header.startswith(b'ID3'):
                    # 读取ID3标签大小
                    size_bytes = header[6:10]
                    size = ((size_bytes[0] & 0x7F) << 21) | ((size_bytes[1] & 0x7F) << 14) | \
                           ((size_bytes[2] & 0x7F) << 7) | (size_bytes[3] & 0x7F)
                    # 跳过ID3标签
                    f.seek(size + 10)
                
                # 查找同步字节
                byte = f.read(1)
                while byte and byte != b'\xFF':
                    byte = f.read(1)
                
                if not byte:
                    return "Unknown"
                
                # 读取帧头
                second_byte = f.read(1)
                if not second_byte:
                    return "Unknown"
                
                frame_header = byte + second_byte + f.read(2)
                if len(frame_header) < 4:
                    return "Unknown"
                
                # 解析帧头
                b1, b2, b3, b4 = frame_header
                
                # 检查MPEG版本
                version_bits = (b2 & 0x18) >> 3
                if version_bits == 0:
                    version = "MPEG 2.5"
                elif version_bits == 2:
                    version = "MPEG 2"
                elif version_bits == 3:
                    version = "MPEG 1"
                else:
                    return "Unknown"
                
                # 检查Layer
                layer_bits = (b2 & 0x06) >> 1
                if layer_bits == 1:
                    layer = "Layer III"
                elif layer_bits == 2:
                    layer = "Layer II"
                elif layer_bits == 3:
                    layer = "Layer I"
                else:
                    return "Unknown"
                
                return f"{version} {layer}"
            
        except Exception as e:
            self.logger.error(f"获取MP3版本失败: {e}")
            return "Unknown"
    
    def load_audio(self, file_path: str) -> Tuple[Optional[Any], Optional[int]]:
        """
        加载音频文件
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Tuple[Optional[Any], Optional[int]]: 音频数据和采样率
        """
        # 尝试使用多种方法加载MP3
        errors = []
        
        # 方法1: 使用librosa
        if backends["librosa"]:
            try:
                audio_data, sample_rate = librosa.load(file_path, sr=None)
                return audio_data, sample_rate
            except Exception as e:
                errors.append(f"librosa加载失败: {e}")
        
        # 方法2: 使用pydub
        if backends["pydub"]:
            try:
                audio = pydub.AudioSegment.from_mp3(file_path)
                sample_rate = audio.frame_rate
                # 转换为numpy数组
                import numpy as np
                audio_data = np.array(audio.get_array_of_samples()) / 32768.0  # 归一化到[-1, 1]
                if audio.channels == 2:
                    audio_data = audio_data.reshape((-1, 2))
                return audio_data, sample_rate
            except Exception as e:
                errors.append(f"pydub加载失败: {e}")
        
        # 方法3: 使用miniaudio
        if backends["miniaudio"]:
            try:
                audio = miniaudio.decode_file(file_path)
                import numpy as np
                audio_data = np.frombuffer(audio.samples, dtype=np.float32)
                if audio.nchannels == 2:
                    audio_data = audio_data.reshape((-1, 2))
                return audio_data, audio.sample_rate
            except Exception as e:
                errors.append(f"miniaudio加载失败: {e}")
        
        # 记录所有错误
        if errors:
            self.logger.error(f"加载MP3文件失败: {'; '.join(errors)}")
        
        return None, None
    
    def get_metadata(self, file_path: str) -> Dict[str, Any]:
        """
        获取音频元数据
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Dict[str, Any]: 元数据字典
        """
        metadata = {
            "title": "",
            "artist": "",
            "album": "",
            "year": "",
            "genre": "",
            "duration": 0,
            "bitrate": 0,
            "sample_rate": 0,
            "channels": 0
        }
        
        errors = []
        
        # 方法1: 使用mutagen
        if backends["mutagen"]:
            try:
                audio = MP3(file_path)
                
                # 获取ID3标签
                if audio.tags:
                    tags = audio.tags
                    
                    # 标题
                    if "TIT2" in tags:
                        metadata["title"] = str(tags["TIT2"])
                    
                    # 艺术家
                    if "TPE1" in tags:
                        metadata["artist"] = str(tags["TPE1"])
                    
                    # 专辑
                    if "TALB" in tags:
                        metadata["album"] = str(tags["TALB"])
                    
                    # 年份
                    if "TDRC" in tags:
                        metadata["year"] = str(tags["TDRC"])
                    
                    # 流派
                    if "TCON" in tags:
                        metadata["genre"] = str(tags["TCON"])
                
                # 技术信息
                metadata["duration"] = audio.info.length
                metadata["bitrate"] = audio.info.bitrate
                metadata["sample_rate"] = audio.info.sample_rate
                metadata["channels"] = audio.info.channels
                
                return metadata
            except Exception as e:
                errors.append(f"mutagen获取元数据失败: {e}")
        
        # 方法2: 使用eyed3
        if backends["eyed3"]:
            try:
                audiofile = eyed3.load(file_path)
                if audiofile and audiofile.tag:
                    metadata["title"] = audiofile.tag.title or ""
                    metadata["artist"] = audiofile.tag.artist or ""
                    metadata["album"] = audiofile.tag.album or ""
                    metadata["year"] = str(audiofile.tag.getBestDate() or "")
                    metadata["genre"] = str(audiofile.tag.genre or "")
                
                if audiofile and audiofile.info:
                    metadata["duration"] = audiofile.info.time_secs
                    metadata["bitrate"] = audiofile.info.bit_rate[1]
                    metadata["sample_rate"] = audiofile.info.sample_freq
                    metadata["channels"] = 2 if audiofile.info.mode == "Stereo" else 1
                
                return metadata
            except Exception as e:
                errors.append(f"eyed3获取元数据失败: {e}")
        
        # 方法3: 使用tinytag
        if backends["tinytag"]:
            try:
                tag = tinytag.TinyTag.get(file_path)
                metadata["title"] = tag.title or ""
                metadata["artist"] = tag.artist or ""
                metadata["album"] = tag.album or ""
                metadata["year"] = tag.year or ""
                metadata["genre"] = tag.genre or ""
                metadata["duration"] = tag.duration
                metadata["bitrate"] = tag.bitrate
                metadata["sample_rate"] = tag.samplerate
                metadata["channels"] = tag.channels
                
                return metadata
            except Exception as e:
                errors.append(f"tinytag获取元数据失败: {e}")
        
        # 记录所有错误
        if errors:
            self.logger.error(f"获取MP3元数据失败: {'; '.join(errors)}")
        
        # 如果所有方法都失败，尝试从文件名获取标题
        if not metadata["title"]:
            metadata["title"] = os.path.basename(file_path).split('.')[0]
        
        return metadata
    
    def get_lyrics(self, file_path: str) -> Optional[str]:
        """
        获取歌词
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            Optional[str]: 歌词文本
        """
        errors = []
        
        # 方法1: 使用mutagen从ID3标签中获取歌词
        if backends["mutagen"]:
            try:
                tags = ID3(file_path)
                
                # 检查USLT标签（非同步歌词）
                if "USLT" in tags:
                    return str(tags["USLT"].text)
                
                # 检查SYLT标签（同步歌词）
                if "SYLT" in tags:
                    # 提取纯文本歌词
                    sylt = tags["SYLT"]
                    lyrics_text = ""
                    for time, text in sylt.text:
                        lyrics_text += text + "\n"
                    return lyrics_text
            except Exception as e:
                errors.append(f"mutagen获取歌词失败: {e}")
        
        # 方法2: 使用eyed3获取歌词
        if backends["eyed3"]:
            try:
                audiofile = eyed3.load(file_path)
                if audiofile and audiofile.tag:
                    lyrics = audiofile.tag.lyrics
                    if lyrics:
                        return "\n".join(l.text for l in lyrics)
            except Exception as e:
                errors.append(f"eyed3获取歌词失败: {e}")
        
        # 方法3: 尝试查找同名LRC文件
        try:
            lrc_path = os.path.splitext(file_path)[0] + ".lrc"
            if os.path.exists(lrc_path):
                with open(lrc_path, 'r', encoding='utf-8') as f:
                    return f.read()
        except Exception as e:
            errors.append(f"读取LRC文件失败: {e}")
        
        # 方法4: 尝试查找同名TXT文件
        try:
            txt_path = os.path.splitext(file_path)[0] + ".txt"
            if os.path.exists(txt_path):
                with open(txt_path, 'r', encoding='utf-8') as f:
                    return f.read()
        except Exception as e:
            errors.append(f"读取TXT文件失败: {e}")
        
        # 记录所有错误
        if errors:
            self.logger.debug(f"获取MP3歌词失败: {'; '.join(errors)}")
        
        return None
