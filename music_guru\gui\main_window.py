#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
主窗口

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import sys
import threading
import logging
import time
from datetime import datetime, timedelta
from PyQt6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                            QLabel, QPushButton, QTreeWidget, QTreeWidgetItem,
                            QCheckBox, QTabWidget, QGroupBox, QFileDialog,
                            QMessageBox, QProgressBar, QSplitter, QTextEdit,
                            QListWidget, QListWidgetItem, QDialog, QDialogButtonBox,
                            QComboBox)
from PyQt6.QtCore import Qt, QSize, pyqtSignal, QTimer
from PyQt6.QtGui import QFont, QIcon

from core.plugin_manager import PluginManager
from core.evaluator import Evaluator
from core.data_model import MusicData
from config.settings import Settings
from gui.widgets.parameter_widget import ParameterWidget
from gui.widgets.result_widget import ResultWidget
from gui.widgets.progress_dialog import ProgressDialog
from gui.widgets.batch_process_dialog import BatchProcessDialog
from utils.audio_format_plugin import AudioFormatManager
from utils.audio_analyzer import AudioAnalyzer
from utils.lyrics_analyzer import LyricsAnalyzer
from utils.batch_processor import BatchProcessor
from utils.lyrics_format_manager import LyricsFormatManager
from plugins.audio_formats.enhanced_mp3_plugin import EnhancedMP3Plugin
from plugins.audio_formats.enhanced_flac_plugin import EnhancedFLACPlugin
from plugins.audio_formats.universal_audio_plugin import UniversalAudioPlugin


class MainWindow(QMainWindow):
    """主窗口类"""

    def __init__(self, plugin_manager: PluginManager, evaluator: Evaluator, settings: Settings):
        super().__init__()

        self.plugin_manager = plugin_manager
        self.evaluator = evaluator
        self.settings = settings
        self.music_data = MusicData()

        # 初始化日志
        self.logger = logging.getLogger("MainWindow")

        # 初始化音频格式管理器
        self.audio_format_manager = AudioFormatManager()
        self.init_audio_format_plugins()

        # 初始化音频分析器
        self.audio_analyzer = AudioAnalyzer()

        # 初始化歌词分析器
        self.lyrics_analyzer = LyricsAnalyzer()

        # 初始化歌词格式管理器
        self.lyrics_format_manager = LyricsFormatManager()

        # 初始化批量处理器
        self.batch_processor = BatchProcessor(self.audio_format_manager, self.evaluator)
        self.batch_processor.progress_updated.connect(self.on_batch_progress_updated)
        self.batch_processor.processing_finished.connect(self.on_batch_processing_finished)
        self.batch_processor.processing_error.connect(self.on_batch_processing_error)

        # 进度对话框
        self.progress_dialog = None

        # 设置窗口属性
        self.setWindowTitle("Music Guru - 流行音乐评价软件")
        self.setMinimumSize(1200, 800)

        # 初始化UI
        self.init_ui()

    def init_audio_format_plugins(self):
        """初始化音频格式插件"""
        # 注册增强版MP3插件
        mp3_plugin = EnhancedMP3Plugin()
        self.audio_format_manager.register_plugin("mp3", mp3_plugin)

        # 注册增强版FLAC插件
        flac_plugin = EnhancedFLACPlugin()
        self.audio_format_manager.register_plugin("flac", flac_plugin)

        # 注册通用音频格式插件
        universal_plugin = UniversalAudioPlugin()
        self.audio_format_manager.register_plugin("universal", universal_plugin)

    def init_ui(self):
        """初始化UI"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 创建顶部工具栏
        top_toolbar = QHBoxLayout()

        # 文件操作按钮
        self.load_button = QPushButton("浏览文件")
        self.load_button.setMinimumWidth(100)
        self.load_button.clicked.connect(self.load_music)
        top_toolbar.addWidget(self.load_button)

        # 批量处理按钮
        self.batch_button = QPushButton("批量处理")
        self.batch_button.setMinimumWidth(100)
        self.batch_button.clicked.connect(self.show_batch_process_dialog)
        top_toolbar.addWidget(self.batch_button)

        # 音乐信息标签
        self.music_info_label = QLabel("未加载音乐文件")
        self.music_info_label.setAlignment(Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter)
        top_toolbar.addWidget(self.music_info_label, 1)

        # 添加顶部工具栏到主布局
        main_layout.addLayout(top_toolbar)

        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter, 1)  # 设置拉伸因子，使分割器占据大部分空间

        # 左侧面板 - 参数选择
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # 添加参数树
        param_group = QGroupBox("评价参数")
        param_layout = QVBoxLayout(param_group)

        self.param_tree = QTreeWidget()
        self.param_tree.setHeaderLabels(["参数", "状态"])
        self.param_tree.setMinimumWidth(400)
        param_layout.addWidget(self.param_tree)

        left_layout.addWidget(param_group)

        # 添加控制按钮
        control_layout = QHBoxLayout()

        self.analyze_button = QPushButton("分析音频")
        self.analyze_button.clicked.connect(self.analyze_current_audio)
        control_layout.addWidget(self.analyze_button)

        self.evaluate_button = QPushButton("开始评价")
        self.evaluate_button.clicked.connect(self.start_evaluation)
        control_layout.addWidget(self.evaluate_button)

        self.save_button = QPushButton("保存结果")
        self.save_button.clicked.connect(self.save_results)
        control_layout.addWidget(self.save_button)

        left_layout.addLayout(control_layout)

        # 右侧面板 - 结果显示
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        # 添加结果标签
        result_label = QLabel("评价结果")
        result_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        result_label.setFont(QFont("Arial", 14, QFont.Weight.Bold))
        right_layout.addWidget(result_label)

        # 添加结果显示区域
        self.result_tabs = QTabWidget()

        # 总分标签页
        self.total_score_widget = ResultWidget()
        self.result_tabs.addTab(self.total_score_widget, "总分")

        # 维度分数标签页
        self.dimension_score_widget = ResultWidget()
        self.result_tabs.addTab(self.dimension_score_widget, "维度分数")

        # 详细结果标签页
        self.detail_widget = QTextEdit()
        self.detail_widget.setReadOnly(True)
        self.result_tabs.addTab(self.detail_widget, "详细结果")

        # 歌词标签页
        lyrics_tab = QWidget()
        lyrics_tab_layout = QVBoxLayout(lyrics_tab)

        # 歌词工具栏
        lyrics_toolbar = QHBoxLayout()

        self.search_lyrics_button = QPushButton("搜索歌词")
        self.search_lyrics_button.clicked.connect(self.search_lyrics)
        lyrics_toolbar.addWidget(self.search_lyrics_button)

        self.load_lyrics_button = QPushButton("加载歌词文件")
        self.load_lyrics_button.clicked.connect(self.load_lyrics_file)
        lyrics_toolbar.addWidget(self.load_lyrics_button)

        self.save_lyrics_button = QPushButton("保存歌词")
        self.save_lyrics_button.clicked.connect(self.save_lyrics_file)
        lyrics_toolbar.addWidget(self.save_lyrics_button)

        lyrics_toolbar.addStretch()

        # 歌词格式选择
        lyrics_toolbar.addWidget(QLabel("格式:"))
        self.lyrics_format_combo = QComboBox()
        self.update_lyrics_format_combo()
        lyrics_toolbar.addWidget(self.lyrics_format_combo)

        lyrics_tab_layout.addLayout(lyrics_toolbar)

        # 歌词文本区域
        self.lyrics_widget = QTextEdit()
        self.lyrics_widget.setReadOnly(False)  # 允许编辑
        lyrics_tab_layout.addWidget(self.lyrics_widget)

        self.result_tabs.addTab(lyrics_tab, "歌词")

        right_layout.addWidget(self.result_tabs)

        # 添加到分割器
        splitter.addWidget(left_panel)
        splitter.addWidget(right_panel)
        splitter.setSizes([400, 800])

        # 底部状态栏
        self.status_bar = self.statusBar()
        self.status_bar.showMessage("就绪")

        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)

        # 加载插件参数
        self.load_plugin_parameters()

    def analyze_current_audio(self):
        """分析当前加载的音频"""
        if not self.music_data.file_path:
            QMessageBox.warning(self, "警告", "请先加载音乐文件")
            return

        # 创建进度对话框
        self.progress_dialog = ProgressDialog(self, "分析音频", 100)
        self.progress_dialog.set_value(0, "准备分析音频...")
        self.progress_dialog.show()

        # 获取适合的音频格式插件
        plugin = self.audio_format_manager.get_plugin_for_file(self.music_data.file_path)
        if not plugin:
            QMessageBox.warning(self, "警告", "不支持的音频格式")
            self.progress_dialog.close()
            self.progress_dialog = None
            self.status_bar.showMessage("分析失败：不支持的音频格式")
            return

        # 更新进度
        self.progress_dialog.set_value(10, "加载音频数据...", f"文件: {os.path.basename(self.music_data.file_path)}")

        # 加载音频数据进行分析
        audio_data, sample_rate = plugin.load_audio(self.music_data.file_path)
        if audio_data is not None and sample_rate is not None:
            self.audio_analyzer.audio_data = audio_data
            self.audio_analyzer.sample_rate = sample_rate
            self.audio_analyzer.duration = len(audio_data) / sample_rate

            # 更新进度
            self.progress_dialog.set_value(30, "分析音频特征...", "计算动态范围、底噪电平等")

            # 分析音频特征
            self.analyze_audio_features()

        # 更新进度
        self.progress_dialog.set_value(70, "分析歌词...", "提取歌词并分析")

        # 获取歌词
        lyrics = plugin.get_lyrics(self.music_data.file_path)
        self.logger.info(f"获取到歌词: {'是' if lyrics else '否'}, 长度: {len(lyrics) if lyrics else 0}")

        if not lyrics:
            # 使用增强的歌词加载功能
            lyrics = self.load_lyrics_from_files(self.music_data.file_path)

        if lyrics:
            # 分析歌词特征
            self.analyze_lyrics_features(lyrics)

            # 显示歌词
            self.lyrics_widget.setText(lyrics)
        else:
            self.logger.warning("无法获取歌词，将使用空歌词")
            # 即使没有歌词，也要更新插件参数，避免使用旧的歌词
            for plugin_id, plugin in self.plugin_manager.get_plugins().items():
                if plugin.dimension == "emotional" and plugin.category == "lyrics_analysis":
                    plugin.parameters["lyrics_text"] = ""

            # 显示提示信息
            supported_formats = ", ".join(self.lyrics_format_manager.get_supported_formats().keys())
            tip_text = (
                "未找到歌词。\n\n"
                "请尝试以下方法：\n"
                "1. 确保音乐文件包含歌词标签\n"
                "2. 在音乐文件同目录下放置同名的歌词文件\n"
                f"   支持格式: {supported_formats}\n"
                "3. 点击\"搜索歌词\"按钮在线搜索歌词\n"  # 添加转义符
                "4. 点击\"加载歌词文件\"按钮手动选择歌词文件"  # 添加转义符
            )
            self.lyrics_widget.setText(tip_text)

        # 完成分析
        self.progress_dialog.set_value(100, "分析完成", "音频分析已完成")

        # 关闭进度对话框
        self.progress_dialog.close()
        self.progress_dialog = None

        # 更新状态栏
        self.status_bar.showMessage("音频分析完成")

    def show_batch_process_dialog(self):
        """显示批量处理对话框"""
        dialog = BatchProcessDialog(self)
        dialog.process_started.connect(self.start_batch_processing)

        if dialog.exec() == QDialog.DialogCode.Accepted:
            pass  # 批量处理已经通过信号启动

    def start_batch_processing(self, source_dir, target_dir, save_results):
        """开始批量处理"""
        # 创建进度对话框
        self.progress_dialog = ProgressDialog(self, "批量处理音乐文件", 100)
        self.progress_dialog.set_value(0, "准备批量处理...", f"源目录: {source_dir}")
        self.progress_dialog.canceled.connect(self.batch_processor.stop_processing)
        self.progress_dialog.show()

        # 在新线程中启动批量处理
        threading.Thread(
            target=self.batch_processor.process_directory,
            args=(source_dir, target_dir, save_results),
            daemon=True
        ).start()

    def on_batch_progress_updated(self, progress, status, detail):
        """批量处理进度更新"""
        if self.progress_dialog:
            self.progress_dialog.set_value(progress, status, detail)

    def on_batch_processing_finished(self, batch_report):
        """批量处理完成"""
        if self.progress_dialog:
            self.progress_dialog.close()
            self.progress_dialog = None

        # 显示报告
        if isinstance(batch_report, dict) and "summary" in batch_report:
            summary = batch_report["summary"]
            statistics = batch_report.get("statistics", {})

            # 创建批量处理报告
            report_text = self.format_batch_report(batch_report)

            # 显示报告对话框
            self.show_batch_report_dialog(report_text, summary)
        else:
            # 兼容旧格式
            count = len(batch_report) if isinstance(batch_report, dict) else 0
            QMessageBox.information(self, "批量处理完成", f"成功处理了 {count} 个文件")

    def on_batch_processing_error(self, file_path, error_message):
        """批量处理错误"""
        self.logger.error(f"处理文件 {file_path} 失败: {error_message}")

    def load_plugin_parameters(self):
        """加载插件参数到树状结构"""
        self.logger.info("加载插件参数到树状结构")
        self.param_tree.clear()

        # 创建根节点
        root_item = QTreeWidgetItem(self.param_tree, ["核心维度"])
        root_item.setExpanded(True)

        # 创建维度节点
        dimension_items = {}
        for dim_id, dim_name in self.plugin_manager.get_dimensions().items():
            dim_item = QTreeWidgetItem(root_item, [dim_name])
            dim_item.setExpanded(True)
            dimension_items[dim_id] = dim_item

        # 添加插件参数
        for plugin_id, plugin in self.plugin_manager.get_plugins().items():
            dim_id = plugin.dimension
            if dim_id in dimension_items:
                # 创建插件节点
                plugin_item = QTreeWidgetItem(dimension_items[dim_id], [plugin.name])

                # 添加复选框
                checkbox = QCheckBox()
                checkbox.setChecked(plugin.enabled)

                # 使用部分函数应用来捕获当前的plugin_id值
                def create_toggle_handler(pid):
                    return lambda state: self.toggle_plugin(pid, state)

                checkbox.stateChanged.connect(create_toggle_handler(plugin_id))
                self.param_tree.setItemWidget(plugin_item, 1, checkbox)

                self.logger.info(f"添加插件: {plugin_id}, 启用状态: {plugin.enabled}")

                # 添加参数
                for param_name, param_value in plugin.get_parameters().items():
                    if param_name != "lyrics_text":  # 不显示歌词文本，太长了
                        param_item = QTreeWidgetItem(plugin_item, [param_name, str(param_value)])
                        self.logger.debug(f"插件 {plugin_id} 参数: {param_name}={param_value}")

    def toggle_plugin(self, plugin_id, state):
        """切换插件启用状态"""
        enabled = state == Qt.CheckState.Checked.value
        self.logger.info(f"切换插件 {plugin_id} 启用状态: {enabled}")
        self.plugin_manager.enable_plugin(plugin_id, enabled)

        # 清除之前的评价结果，以便下次点击"开始评价"按钮时重新执行评价
        if hasattr(self.evaluator, 'results'):
            self.evaluator.results = {}
            self.logger.info("清除之前的评价结果")

    def reset_evaluation(self):
        """复位评价状态到初始状态"""
        self.logger.info("=== 开始评价复位 ===")

        # 1. 重置音乐数据对象
        from core.data_model import MusicData
        old_file_path = getattr(self.music_data, 'file_path', '')
        self.music_data = MusicData()
        self.logger.info("重置音乐数据对象")

        # 2. 清空音频分析器
        if hasattr(self, 'audio_analyzer'):
            self.audio_analyzer.audio_data = None
            self.audio_analyzer.sample_rate = None
            self.audio_analyzer.duration = 0
            self.logger.info("清空音频分析器数据")

        # 3. 清空评价器结果
        if hasattr(self, 'evaluator'):
            if hasattr(self.evaluator, 'results'):
                self.evaluator.results = {}
            if hasattr(self.evaluator, 'dimension_scores'):
                self.evaluator.dimension_scores = {}
            if hasattr(self.evaluator, 'total_score'):
                self.evaluator.total_score = 0.0
            self.logger.info("清空评价器结果")

        # 4. 重置UI显示
        self.reset_ui_display()

        # 5. 重置插件状态
        self.reset_plugin_states()

        self.logger.info("=== 评价复位完成 ===")

    def reset_ui_display(self):
        """重置UI显示状态"""
        try:
            # 重置音乐信息标签
            self.music_info_label.setText("未加载音乐文件")

            # 重置结果显示
            if hasattr(self, 'total_score_widget'):
                self.total_score_widget.set_score(0.0)

            if hasattr(self, 'dimension_score_widget'):
                self.dimension_score_widget.set_dimension_scores({})

            # 清空详细结果
            if hasattr(self, 'detail_widget'):
                self.detail_widget.clear()

            # 清空歌词显示
            if hasattr(self, 'lyrics_widget'):
                self.lyrics_widget.clear()

            # 重置状态栏
            self.status_bar.showMessage("就绪")

            # 隐藏进度条
            if hasattr(self, 'progress_bar'):
                self.progress_bar.setVisible(False)
                self.progress_bar.setValue(0)

            self.logger.info("UI显示状态已重置")

        except Exception as e:
            self.logger.error(f"重置UI显示失败: {e}")

    def reset_plugin_states(self):
        """重置插件状态"""
        try:
            # 重置所有插件的内部状态
            if hasattr(self, 'plugin_manager'):
                enabled_plugins = self.plugin_manager.get_enabled_plugins()
                for plugin_name, plugin in enabled_plugins.items():
                    # 重置插件参数
                    if hasattr(plugin, 'parameters'):
                        plugin.parameters = {}

                    # 重置插件缓存
                    if hasattr(plugin, 'cache'):
                        plugin.cache = {}

                    # 调用插件的重置方法（如果存在）
                    if hasattr(plugin, 'reset'):
                        plugin.reset()

                self.logger.info(f"已重置 {len(enabled_plugins)} 个插件状态")

        except Exception as e:
            self.logger.error(f"重置插件状态失败: {e}")

    def load_music(self):
        """加载音乐文件"""
        # 首先执行评价复位
        self.reset_evaluation()

        # 获取文件过滤器
        file_filter = self.audio_format_manager.get_file_filter()

        # 打开文件对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择音乐文件", "", file_filter
        )

        if not file_path:
            return

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(10)
        self.status_bar.showMessage("正在加载音乐文件...")

        # 获取适合的音频格式插件
        plugin = self.audio_format_manager.get_plugin_for_file(file_path)
        if not plugin:
            QMessageBox.warning(self, "警告", "不支持的音频格式")
            self.progress_bar.setVisible(False)
            self.status_bar.showMessage("加载失败：不支持的音频格式")
            return

        # 获取音频格式信息
        format_version = plugin.get_version(file_path)
        self.logger.info(f"音频格式: {plugin.name}, 版本: {format_version}")

        # 加载音频文件
        self.music_data.file_path = file_path
        self.progress_bar.setValue(20)

        # 获取元数据
        metadata = plugin.get_metadata(file_path)
        self.music_data.title = metadata.get("title", os.path.basename(file_path).split('.')[0])
        self.music_data.artist = metadata.get("artist", "")
        self.music_data.album = metadata.get("album", "")
        self.music_data.release_date = metadata.get("year", "")
        self.music_data.genre = metadata.get("genre", "")
        self.progress_bar.setValue(40)

        # 加载音频数据进行分析
        self.status_bar.showMessage("正在分析音频...")
        audio_data, sample_rate = plugin.load_audio(file_path)
        if audio_data is not None and sample_rate is not None:
            self.audio_analyzer.audio_data = audio_data
            self.audio_analyzer.sample_rate = sample_rate
            self.audio_analyzer.duration = len(audio_data) / sample_rate

            # 分析音频特征
            self.analyze_audio_features()
        self.progress_bar.setValue(70)

        # 获取歌词
        self.status_bar.showMessage("正在分析歌词...")
        lyrics = plugin.get_lyrics(file_path)
        if lyrics:
            # 分析歌词特征
            self.analyze_lyrics_features(lyrics)
        self.progress_bar.setValue(90)

        # 更新UI
        self.update_music_info()

        # 隐藏进度条
        self.progress_bar.setVisible(False)
        self.status_bar.showMessage(f"已加载: {self.music_data.title}")

    def analyze_audio_features(self):
        """分析音频特征"""
        try:
            # 分析动态范围
            dynamic_range = self.audio_analyzer.analyze_dynamic_range()
            self.music_data.production_data["recording_quality"]["dynamic_range"] = dynamic_range

            # 分析底噪电平
            noise_floor = self.audio_analyzer.analyze_noise_floor()
            self.music_data.production_data["recording_quality"]["noise_floor"] = noise_floor

            # 分析前奏吸引力峰值出现时间
            intro_hook_timing = self.audio_analyzer.analyze_intro_hook_timing()
            self.music_data.market_data["streaming_adaptation"]["intro_hook_timing"] = intro_hook_timing

            # 分析副歌音高范围
            chorus_range = self.audio_analyzer.analyze_chorus_range()
            self.music_data.emotional_data["emotion_delivery"]["chorus_range"] = chorus_range

        except Exception as e:
            print(f"音频分析失败: {e}")

    def analyze_lyrics_features(self, lyrics_text):
        """分析歌词特征"""
        try:
            self.logger.info(f"开始分析歌词特征，歌词长度: {len(lyrics_text)}")

            # 加载歌词
            if self.lyrics_analyzer.load_lyrics(lyrics_text):
                self.logger.info(f"歌词加载成功，行数: {len(self.lyrics_analyzer.lines)}, 词数: {self.lyrics_analyzer.word_count}")

                # 分析年度热词覆盖率
                trending_words_ratio = self.lyrics_analyzer.analyze_trending_words_ratio()
                self.music_data.emotional_data["era_resonance"]["trending_words_ratio"] = trending_words_ratio
                self.logger.info(f"年度热词覆盖率: {trending_words_ratio:.4f}")

                # 分析通感修辞数量
                synesthesia_count = self.lyrics_analyzer.analyze_synesthesia_count()
                self.music_data.emotional_data["emotion_delivery"]["synesthesia_count"] = synesthesia_count
                self.logger.info(f"通感修辞数量: {synesthesia_count}")

                # 分析具象场景描写密度
                scene_density = self.lyrics_analyzer.analyze_scene_density()
                self.music_data.emotional_data["narrative_tension"]["scene_density"] = scene_density
                self.logger.info(f"具象场景描写密度: {scene_density:.4f}")

                # 分析歌词语义模糊度
                lyrics_clarity = self.lyrics_analyzer.analyze_lyrics_clarity()
                self.music_data.emotional_data["narrative_tension"]["lyrics_clarity"] = lyrics_clarity
                self.logger.info(f"歌词语义模糊度: {lyrics_clarity:.4f}")

                # 分析情感转折点是否符合三幕剧结构
                plot_structure = self.lyrics_analyzer.analyze_plot_structure()
                self.music_data.emotional_data["narrative_tension"]["plot_structure"] = plot_structure
                self.logger.info(f"情感转折点符合三幕剧结构: {plot_structure}")

                # 保存歌词到情感表达插件
                for plugin_id, plugin in self.plugin_manager.get_plugins().items():
                    if plugin.dimension == "emotional" and plugin.category == "lyrics_analysis":
                        self.logger.info(f"将歌词传递给插件: {plugin_id}")
                        plugin.parameters["lyrics_text"] = lyrics_text

                        # 更新插件参数
                        plugin.parameters["rhyme_scheme"] = trending_words_ratio
                        plugin.parameters["metaphor_density"] = synesthesia_count / max(len(self.lyrics_analyzer.lines), 1)
                        plugin.parameters["vocabulary_richness"] = 1 - lyrics_clarity
                        plugin.parameters["international_appeal"] = scene_density / 10

                        self.logger.info(f"更新插件参数: rhyme_scheme={plugin.parameters['rhyme_scheme']:.4f}, "
                                        f"metaphor_density={plugin.parameters['metaphor_density']:.4f}, "
                                        f"vocabulary_richness={plugin.parameters['vocabulary_richness']:.4f}, "
                                        f"international_appeal={plugin.parameters['international_appeal']:.4f}")
            else:
                self.logger.warning("歌词加载失败")
        except Exception as e:
            error_msg = f"歌词分析失败: {e}"
            self.logger.error(error_msg, exc_info=True)
            print(error_msg)

    def update_music_info(self):
        """更新音乐信息显示"""
        # 更新窗口标题
        if self.music_data.artist:
            self.setWindowTitle(f"Music Guru - {self.music_data.title} - {self.music_data.artist}")
        else:
            self.setWindowTitle(f"Music Guru - {self.music_data.title}")

        # 更新音乐信息标签
        info_text = f"<b>{self.music_data.title}</b>"

        if self.music_data.artist:
            info_text += f" - <b>{self.music_data.artist}</b>"

        if self.music_data.album:
            info_text += f" | 专辑: {self.music_data.album}"

        if self.music_data.genre:
            info_text += f" | 流派: {self.music_data.genre}"

        if self.music_data.release_date:
            info_text += f" | 发行日期: {self.music_data.release_date}"

        # 获取音频格式信息
        if self.music_data.file_path:
            plugin = self.audio_format_manager.get_plugin_for_file(self.music_data.file_path)
            if plugin:
                format_version = plugin.get_version(self.music_data.file_path)
                info_text += f" | 格式: {format_version}"

        self.music_info_label.setText(info_text)

    def start_evaluation(self):
        """开始评价"""
        if not self.music_data.file_path:
            QMessageBox.warning(self, "警告", "请先加载音乐文件")
            return

        # 获取已启用的插件
        enabled_plugins = self.plugin_manager.get_enabled_plugins()
        if not enabled_plugins:
            QMessageBox.warning(self, "警告", "请至少选择一个评价参数")
            return

        self.logger.info(f"开始评价: {self.music_data.title}")
        self.logger.info(f"已启用的插件: {list(enabled_plugins.keys())}")

        # 创建进度对话框
        self.progress_dialog = ProgressDialog(self, "评价音乐", 100)
        self.progress_dialog.set_value(0, "准备评价...")
        self.progress_dialog.show()

        # 更新进度
        self.progress_dialog.set_value(20, "执行评价...", "计算各维度得分")

        # 执行评价
        self.evaluator.evaluate(self.music_data)

        # 更新进度
        self.progress_dialog.set_value(60, "计算总分...", "计算加权平均分")

        # 计算总分
        total_score = self.evaluator.calculate_total_score()
        dimension_scores = self.evaluator.calculate_dimension_scores()

        self.logger.info(f"评价结果: 总分={total_score:.2f}, 维度分数={dimension_scores}")

        # 更新进度
        self.progress_dialog.set_value(80, "更新结果显示...", "更新UI")

        # 更新结果显示
        self.total_score_widget.set_score(total_score)
        self.dimension_score_widget.set_dimension_scores(dimension_scores)

        # 更新详细结果
        detailed_results = self.evaluator.get_detailed_results()
        formatted_results = self.format_detailed_results(detailed_results)
        self.detail_widget.setText(formatted_results)

        # 完成评价
        self.progress_dialog.set_value(100, "评价完成", f"总分: {total_score:.2f}")

        # 关闭进度对话框
        self.progress_dialog.close()
        self.progress_dialog = None

        # 更新状态栏
        self.status_bar.showMessage(f"评价完成，总分: {total_score:.2f}")

    def update_lyrics_format_combo(self):
        """更新歌词格式下拉框"""
        self.lyrics_format_combo.clear()
        formats = self.lyrics_format_manager.get_supported_formats()
        for ext, name in formats.items():
            self.lyrics_format_combo.addItem(f"{name} ({ext})", ext)

    def search_lyrics(self):
        """搜索歌词"""
        if not self.music_data.title:
            QMessageBox.warning(self, "警告", "请先加载音乐文件")
            return

        # 导入搜索对话框
        try:
            from gui.widgets.lyrics_search_dialog import LyricsSearchDialog

            dialog = LyricsSearchDialog(self, self.music_data.artist, self.music_data.title)
            dialog.lyrics_selected.connect(self.on_lyrics_selected)
            dialog.exec()
        except ImportError as e:
            QMessageBox.critical(self, "错误", f"无法加载歌词搜索功能: {e}")

    def on_lyrics_selected(self, lyrics, metadata):
        """处理选中的歌词"""
        # 显示歌词
        self.lyrics_widget.setText(lyrics)

        # 如果需要保存歌词
        if metadata.get("save_lyrics", False):
            save_format = metadata.get("save_format", "txt")

            if self.music_data.file_path:
                # 生成保存路径
                base_path = os.path.splitext(self.music_data.file_path)[0]
                save_path = f"{base_path}.{save_format}"

                # 保存歌词
                success = self.lyrics_format_manager.write_lyrics(save_path, lyrics, save_format, metadata)
                if success:
                    self.logger.info(f"歌词已保存到: {save_path}")
                    QMessageBox.information(self, "保存成功", f"歌词已保存到: {save_path}")
                else:
                    QMessageBox.warning(self, "保存失败", "保存歌词时出错")

        # 重新分析歌词特征
        self.analyze_lyrics_features(lyrics)

    def load_lyrics_file(self):
        """加载歌词文件"""
        # 获取支持的格式
        formats = self.lyrics_format_manager.get_supported_formats()
        filter_parts = []
        for ext, name in formats.items():
            filter_parts.append(f"{name} (*{ext})")

        file_filter = ";;".join(filter_parts) + ";;所有文件 (*)"

        # 打开文件对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择歌词文件", "", file_filter
        )

        if not file_path:
            return

        # 读取歌词
        lyrics = self.lyrics_format_manager.read_lyrics(file_path)
        if lyrics:
            self.lyrics_widget.setText(lyrics)
            self.logger.info(f"成功加载歌词文件: {file_path}")

            # 重新分析歌词特征
            self.analyze_lyrics_features(lyrics)
        else:
            QMessageBox.warning(self, "加载失败", "无法读取歌词文件")

    def save_lyrics_file(self):
        """保存歌词文件"""
        lyrics = self.lyrics_widget.toPlainText().strip()
        if not lyrics:
            QMessageBox.warning(self, "警告", "没有歌词内容可保存")
            return

        # 获取选中的格式
        format_data = self.lyrics_format_combo.currentData()
        if not format_data:
            format_data = ".txt"

        format_name = format_data.lstrip('.')

        # 生成默认文件名
        if self.music_data.title:
            default_name = f"{self.music_data.title}.{format_name}"
        else:
            default_name = f"歌词.{format_name}"

        # 打开保存对话框
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存歌词文件", default_name,
            f"歌词文件 (*{format_data});;所有文件 (*)"
        )

        if not file_path:
            return

        # 准备元数据
        metadata = {
            "title": self.music_data.title,
            "artist": self.music_data.artist,
            "album": self.music_data.album,
            "year": self.music_data.release_date,
            "genre": self.music_data.genre
        }

        # 保存歌词
        success = self.lyrics_format_manager.write_lyrics(file_path, lyrics, format_name, metadata)
        if success:
            self.logger.info(f"歌词已保存到: {file_path}")
            QMessageBox.information(self, "保存成功", f"歌词已保存到: {file_path}")
        else:
            QMessageBox.critical(self, "保存失败", "保存歌词时出错")

    def load_lyrics_from_files(self, music_file_path):
        """从文件加载歌词（增强版）"""
        # 使用歌词格式管理器查找歌词文件
        found_files = self.lyrics_format_manager.find_lyrics_files(music_file_path)

        if found_files:
            # 优先使用第一个找到的文件
            lyrics_path, plugin = found_files[0]
            lyrics = plugin.read_lyrics(lyrics_path)

            if lyrics:
                self.logger.info(f"从文件加载歌词: {lyrics_path}")
                return lyrics

        return None

    def format_detailed_results(self, results):
        """格式化详细结果为易读的文本"""
        if not results:
            return "暂无评价结果"

        formatted_text = []

        # 添加标题
        formatted_text.append("=" * 60)
        formatted_text.append("音乐评价详细报告")
        formatted_text.append("=" * 60)
        formatted_text.append("")

        # 总分
        total_score = results.get('total_score', 0)
        formatted_text.append(f"🎵 总体评分: {total_score:.2f}/100")
        formatted_text.append("")

        # 维度得分
        dimension_scores = results.get('dimension_scores', {})
        if dimension_scores:
            formatted_text.append("📊 各维度得分:")
            formatted_text.append("-" * 40)

            dimension_names = {
                'technical': '技术维度',
                'emotional': '情感维度',
                'market': '市场维度',
                'cultural': '文化维度',
                'production': '制作维度'
            }

            for dim_id, score in dimension_scores.items():
                dim_name = dimension_names.get(dim_id, dim_id)
                formatted_text.append(f"  {dim_name}: {score:.2f}/100")

            formatted_text.append("")

        # 插件详细结果
        plugin_results = results.get('plugin_results', {})
        if plugin_results:
            formatted_text.append("🔍 详细分析结果:")
            formatted_text.append("-" * 40)

            # 按维度分组显示
            dimensions = {}
            for plugin_id, plugin_result in plugin_results.items():
                dimension = plugin_result.get('dimension', 'unknown')
                if dimension not in dimensions:
                    dimensions[dimension] = []
                dimensions[dimension].append((plugin_id, plugin_result))

            dimension_names = {
                'technical': '🔧 技术维度',
                'emotional': '❤️ 情感维度',
                'market': '📈 市场维度',
                'cultural': '🌍 文化维度',
                'production': '🎛️ 制作维度'
            }

            for dim_id, plugins in dimensions.items():
                dim_name = dimension_names.get(dim_id, f"📋 {dim_id}")
                formatted_text.append(f"\n{dim_name}")
                formatted_text.append("─" * 30)

                for plugin_id, plugin_result in plugins:
                    name = plugin_result.get('name', plugin_id)
                    score = plugin_result.get('score', 0)
                    details = plugin_result.get('details', {})

                    # 插件名称和得分
                    formatted_text.append(f"\n  📌 {name}: {score:.2f}/100")

                    # 详细评分项
                    if details:
                        for detail_key, detail_value in details.items():
                            # 格式化详细项名称
                            detail_names = {
                                'formula_usage': '公式化套用',
                                'key_changes': '调式转换',
                                'alt_chord_ratio': '替代和弦',
                                'interval_range': '音程跨度',
                                'hook_density': 'Hook密度',
                                'reverse_melody': '逆向旋律',
                                'chorus_range': '副歌音域',
                                'dynamic_range': '动态范围',
                                'synesthesia_count': '通感修辞',
                                'rhyme_scheme': '押韵方案',
                                'metaphor_density': '隐喻密度',
                                'vocabulary_richness': '词汇丰富度',
                                'international_appeal': '国际传播',
                                'intro_hook_timing': '前奏吸引力',
                                'mobile_optimization': '移动端优化',
                                'cross_genre_ratio': '跨流派元素',
                                'subculture_density': '亚文化符号',
                                'ai_content_originality': 'AI原创性',
                                'noise_floor': '底噪电平',
                                'apple_masters': 'Apple认证'
                            }

                            detail_name = detail_names.get(detail_key, detail_key)

                            # 根据得分情况添加表情符号
                            if "+0分" in str(detail_value) or "不符合" in str(detail_value) or "未通过" in str(detail_value):
                                emoji = "❌"
                            elif "+1分" in str(detail_value):
                                emoji = "⚠️"
                            elif "+2分" in str(detail_value) or "+3分" in str(detail_value):
                                emoji = "✅"
                            elif "+4分" in str(detail_value) or "+5分" in str(detail_value):
                                emoji = "🌟"
                            else:
                                emoji = "ℹ️"

                            formatted_text.append(f"    {emoji} {detail_name}: {detail_value}")

        # 添加建议
        formatted_text.append("\n" + "=" * 60)
        formatted_text.append("💡 改进建议:")
        formatted_text.append("=" * 60)

        # 根据得分情况给出建议
        if total_score < 30:
            formatted_text.append("🔴 整体评分较低，建议从以下方面改进：")
            formatted_text.append("  • 增强歌词的押韵和隐喻表达")
            formatted_text.append("  • 提升音乐的动态范围和录音质量")
            formatted_text.append("  • 优化旋律结构和和声进行")
        elif total_score < 60:
            formatted_text.append("🟡 整体评分中等，有较大提升空间：")
            formatted_text.append("  • 加强情感表达和文化元素融合")
            formatted_text.append("  • 优化流媒体平台适配性")
            formatted_text.append("  • 提升制作质量和技术水准")
        else:
            formatted_text.append("🟢 整体评分良好，可进一步优化：")
            formatted_text.append("  • 在保持优势的基础上补强薄弱环节")
            formatted_text.append("  • 探索更多创新元素和风格融合")
            formatted_text.append("  • 持续提升整体完成度")

        formatted_text.append("")
        formatted_text.append("📝 注：评分基于当前音频分析结果，实际效果可能因播放设备和环境而异")

        return "\n".join(formatted_text)

    def format_batch_report(self, batch_report):
        """格式化批量处理报告"""
        formatted_text = []

        # 添加标题
        formatted_text.append("=" * 60)
        formatted_text.append("批量音乐评价报告")
        formatted_text.append("=" * 60)
        formatted_text.append("")

        # 处理摘要
        summary = batch_report.get("summary", {})
        total_files = summary.get("total_files", 0)
        processed_files = summary.get("processed_files", 0)
        failed_files = summary.get("failed_files", 0)

        formatted_text.append("📊 处理摘要:")
        formatted_text.append("-" * 40)
        formatted_text.append(f"  总文件数: {total_files}")
        formatted_text.append(f"  成功处理: {processed_files}")
        formatted_text.append(f"  处理失败: {failed_files}")
        if total_files > 0:
            success_rate = (processed_files / total_files) * 100
            formatted_text.append(f"  成功率: {success_rate:.1f}%")
        formatted_text.append("")

        # 统计信息
        statistics = batch_report.get("statistics", {})
        if statistics:
            formatted_text.append("📈 统计分析:")
            formatted_text.append("-" * 40)

            avg_score = statistics.get("average_score", 0)
            max_score = statistics.get("max_score", 0)
            min_score = statistics.get("min_score", 0)

            formatted_text.append(f"  平均分: {avg_score:.2f}/100")
            formatted_text.append(f"  最高分: {max_score:.2f}/100")
            formatted_text.append(f"  最低分: {min_score:.2f}/100")
            formatted_text.append("")

            # 分数分布
            score_dist = statistics.get("score_distribution", {})
            if score_dist:
                formatted_text.append("🎯 分数分布:")
                formatted_text.append(f"  优秀 (80-100分): {score_dist.get('excellent', 0)} 首")
                formatted_text.append(f"  良好 (60-79分): {score_dist.get('good', 0)} 首")
                formatted_text.append(f"  一般 (40-59分): {score_dist.get('average', 0)} 首")
                formatted_text.append(f"  较差 (0-39分): {score_dist.get('poor', 0)} 首")
                formatted_text.append("")

            # 维度平均分
            dim_averages = statistics.get("dimension_averages", {})
            if dim_averages:
                formatted_text.append("🔍 各维度平均分:")
                dimension_names = {
                    'technical': '技术维度',
                    'emotional': '情感维度',
                    'market': '市场维度',
                    'cultural': '文化维度',
                    'production': '制作维度'
                }

                for dim_id, avg_score in dim_averages.items():
                    dim_name = dimension_names.get(dim_id, dim_id)
                    formatted_text.append(f"  {dim_name}: {avg_score:.2f}/100")
                formatted_text.append("")

        # 详细结果（只显示前10个）
        results = batch_report.get("results", {})
        if results:
            formatted_text.append("📋 详细结果 (前10首):")
            formatted_text.append("-" * 40)

            count = 0
            for file_path, result in results.items():
                if count >= 10:
                    break

                file_name = result.get("file_name", file_path)
                evaluation = result.get("evaluation", {})
                total_score = evaluation.get("total_score", 0) if evaluation else 0

                # 根据分数添加表情符号
                if total_score >= 80:
                    emoji = "🌟"
                elif total_score >= 60:
                    emoji = "✅"
                elif total_score >= 40:
                    emoji = "⚠️"
                else:
                    emoji = "❌"

                formatted_text.append(f"  {emoji} {file_name}: {total_score:.2f}/100")
                count += 1

            if len(results) > 10:
                formatted_text.append(f"  ... 还有 {len(results) - 10} 个文件")
            formatted_text.append("")

        # 建议
        formatted_text.append("💡 批量处理建议:")
        formatted_text.append("-" * 40)
        if statistics:
            avg_score = statistics.get("average_score", 0)
            if avg_score < 40:
                formatted_text.append("🔴 整体质量较低，建议:")
                formatted_text.append("  • 重新审视音乐制作流程")
                formatted_text.append("  • 加强歌词创作和音乐编排")
                formatted_text.append("  • 提升录音和后期制作质量")
            elif avg_score < 60:
                formatted_text.append("🟡 整体质量中等，建议:")
                formatted_text.append("  • 针对薄弱维度进行重点改进")
                formatted_text.append("  • 学习高分作品的优秀特征")
                formatted_text.append("  • 保持创作风格的一致性")
            else:
                formatted_text.append("🟢 整体质量良好，建议:")
                formatted_text.append("  • 继续保持现有优势")
                formatted_text.append("  • 探索更多创新元素")
                formatted_text.append("  • 关注市场和文化适应性")

        formatted_text.append("")
        formatted_text.append("📝 注：详细的单首分析结果已保存到指定目录")

        return "\n".join(formatted_text)

    def show_batch_report_dialog(self, report_text, summary):
        """显示批量处理报告对话框"""
        dialog = QDialog(self)

        # 根据处理结果设置窗口标题
        total_files = summary.get("total_files", 0)
        processed_files = summary.get("processed_files", 0)
        dialog.setWindowTitle(f"批量处理报告 - 处理了 {processed_files}/{total_files} 个文件")
        dialog.setMinimumSize(800, 600)

        layout = QVBoxLayout(dialog)

        # 报告文本
        text_widget = QTextEdit()
        text_widget.setPlainText(report_text)
        text_widget.setReadOnly(True)
        text_widget.setFont(QFont("Consolas", 10))
        layout.addWidget(text_widget)

        # 按钮
        button_layout = QHBoxLayout()

        save_button = QPushButton("保存报告")
        save_button.clicked.connect(lambda: self.save_batch_report(report_text))
        button_layout.addWidget(save_button)

        close_button = QPushButton("关闭")
        close_button.clicked.connect(dialog.accept)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)

        dialog.exec()

    def save_batch_report(self, report_text):
        """保存批量处理报告"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存批量处理报告",
            f"批量处理报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "文本文件 (*.txt);;所有文件 (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(report_text)
                QMessageBox.information(self, "保存成功", f"报告已保存到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "保存失败", f"保存报告时出错: {e}")

    def save_results(self):
        """保存评价结果"""
        if not hasattr(self.evaluator, 'results') or not self.evaluator.results:
            QMessageBox.warning(self, "警告", "没有可保存的评价结果")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存评价结果", "", "JSON文件 (*.json);;所有文件 (*.*)"
        )

        if file_path:
            detailed_results = self.evaluator.get_detailed_results()

            try:
                import json
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(detailed_results, f, indent=4, ensure_ascii=False)

                self.status_bar.showMessage(f"结果已保存到: {file_path}")
            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存结果失败: {e}")
