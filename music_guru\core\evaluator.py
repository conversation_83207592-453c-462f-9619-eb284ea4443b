#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
评价引擎

MIT License
Copyright (c) 2025 Music Guru
"""

import logging
from typing import Dict, List
from core.plugin_manager import PluginManager, Plugin
from config.settings import Settings


class EvaluationResult:
    """评价结果类"""

    def __init__(self, plugin_id, plugin_name, dimension, category, score, details):
        self.plugin_id = plugin_id
        self.plugin_name = plugin_name
        self.dimension = dimension
        self.category = category
        self.score = score
        self.details = details


class Evaluator:
    """评价引擎"""

    def __init__(self, plugin_manager: PluginManager, settings: Settings = None):
        """初始化评价引擎"""
        self.plugin_manager = plugin_manager
        self.settings = settings or Settings()
        self.results = {}  # 评价结果
        self.logger = logging.getLogger("Evaluator")

    def evaluate(self, data, plugins=None) -> Dict[str, EvaluationResult]:
        """
        执行评价

        Args:
            data: 评价数据
            plugins: 要使用的插件ID列表，如果为None则使用所有已启用的插件

        Returns:
            Dict[str, EvaluationResult]: 评价结果
        """
        # 清空之前的结果
        self.results = {}

        self.logger.info(f"开始评价，数据: {data.title}")

        # 获取要使用的插件
        if plugins is None:
            plugins_to_use = self.plugin_manager.get_enabled_plugins()
            self.logger.info(f"使用所有已启用的插件，共 {len(plugins_to_use)} 个")
        else:
            plugins_to_use = {pid: self.plugin_manager.get_plugin(pid)
                             for pid in plugins if self.plugin_manager.get_plugin(pid)}
            self.logger.info(f"使用指定的插件，共 {len(plugins_to_use)} 个")

        # 记录插件列表
        for plugin_id, plugin in plugins_to_use.items():
            self.logger.info(f"插件: {plugin_id} ({plugin.name}), 维度: {plugin.dimension}, 分类: {plugin.category}")

        # 执行每个插件的评价
        for plugin_id, plugin in plugins_to_use.items():
            try:
                self.logger.info(f"执行插件 {plugin_id} 的评价")

                # 记录插件参数
                self.logger.debug(f"插件 {plugin_id} 的参数: {plugin.parameters}")

                # 执行评价
                result = plugin.evaluate(data)

                # 记录评价结果
                self.logger.info(f"插件 {plugin_id} 评价结果: 分数={result.get('score', 0)}")
                self.logger.debug(f"插件 {plugin_id} 评价详情: {result.get('details', {})}")

                # 创建评价结果对象
                evaluation_result = EvaluationResult(
                    plugin_id=plugin_id,
                    plugin_name=plugin.name,
                    dimension=plugin.dimension,
                    category=plugin.category,
                    score=result.get('score', 0),
                    details=result.get('details', {})
                )

                # 保存结果
                self.results[plugin_id] = evaluation_result

            except Exception as e:
                error_msg = f"插件 {plugin_id} 评价失败: {e}"
                self.logger.error(error_msg, exc_info=True)
                print(error_msg)

        # 记录评价结果
        self.logger.info(f"评价完成，共 {len(self.results)} 个结果")

        return self.results

    def calculate_dimension_scores(self) -> Dict[str, float]:
        """计算各维度得分"""
        dimension_scores = {}
        dimension_counts = {}

        # 计算每个维度的总分和插件数量
        for result in self.results.values():
            dimension = result.dimension

            if dimension not in dimension_scores:
                dimension_scores[dimension] = 0
                dimension_counts[dimension] = 0

            dimension_scores[dimension] += result.score
            dimension_counts[dimension] += 1

        # 计算平均分
        for dimension in dimension_scores:
            if dimension_counts[dimension] > 0:
                dimension_scores[dimension] /= dimension_counts[dimension]

        return dimension_scores

    def calculate_total_score(self) -> float:
        """计算总分（加权平均）"""
        dimension_scores = self.calculate_dimension_scores()
        total_score = 0.0
        total_weight = 0.0

        # 应用动态校准机制
        cultural_score = dimension_scores.get('cultural', 0)
        market_weight = self.settings.get_dimension_weight('market')

        # 如果文化创新得分大于80分，市场传播权重提升
        if cultural_score > 80:
            market_weight += self.settings.calibration.get('cultural_market_boost', 0.05)

        # 计算加权平均分
        for dimension, score in dimension_scores.items():
            weight = self.settings.get_dimension_weight(dimension)

            # 应用市场传播权重调整
            if dimension == 'market':
                weight = market_weight

            total_score += score * weight
            total_weight += weight

        # 避免除以零
        if total_weight > 0:
            return total_score / total_weight
        return 0.0

    def get_detailed_results(self) -> Dict:
        """获取详细评价结果"""
        dimension_scores = self.calculate_dimension_scores()
        total_score = self.calculate_total_score()

        # 构建详细结果
        detailed_results = {
            'total_score': total_score,
            'dimension_scores': dimension_scores,
            'plugin_results': {pid: {
                'name': result.plugin_name,
                'dimension': result.dimension,
                'category': result.category,
                'score': result.score,
                'details': result.details
            } for pid, result in self.results.items()}
        }

        return detailed_results
