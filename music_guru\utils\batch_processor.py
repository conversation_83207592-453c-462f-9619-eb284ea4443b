#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
批量处理器

MIT License
Copyright (c) 2025 Music Guru
"""

import os
import json
import logging
import time
from typing import List, Dict, Any, Callable, Optional
from PyQt6.QtCore import QObject, pyqtSignal

from utils.audio_format_plugin import AudioFormatManager
from core.evaluator import Evaluator
from core.data_model import MusicData


class BatchProcessor(QObject):
    """批量处理器"""

    # 自定义信号
    progress_updated = pyqtSignal(int, str, str)  # 进度值, 状态文本, 详细信息
    processing_finished = pyqtSignal(object)  # 处理结果，使用 object 代替 Dict[str, Any]
    processing_error = pyqtSignal(str, str)  # 错误文件, 错误信息

    def __init__(self, audio_format_manager: AudioFormatManager, evaluator: Evaluator):
        super().__init__()

        self.audio_format_manager = audio_format_manager
        self.evaluator = evaluator
        self.logger = logging.getLogger("BatchProcessor")

        # 处理状态
        self.is_processing = False
        self.should_stop = False

        # 处理结果
        self.results = {}

        # 文件扩展名
        self.file_extensions = [".mp3", ".flac", ".wav", ".ogg", ".m4a", ".aac", ".wma"]

    def process_directory(self, source_dir: str, target_dir: str = "", save_results: bool = True,
                         file_extensions: Optional[List[str]] = None):
        """
        处理目录中的所有音乐文件

        Args:
            source_dir: 源目录
            target_dir: 目标目录
            save_results: 是否保存结果
            file_extensions: 文件扩展名列表
        """
        if self.is_processing:
            self.logger.warning("已有处理任务正在进行")
            return

        self.is_processing = True
        self.should_stop = False
        self.results = {}

        # 使用默认文件扩展名列表
        if file_extensions is None:
            file_extensions = self.file_extensions

        # 查找所有符合条件的文件
        files = []
        for root, dirs, filenames in os.walk(source_dir):
            for filename in filenames:
                file_ext = os.path.splitext(filename)[1].lower()
                if file_ext in file_extensions:
                    file_path = os.path.join(root, filename)
                    rel_path = os.path.relpath(file_path, source_dir)
                    files.append((file_path, rel_path))

        # 处理文件
        total_files = len(files)
        self.logger.info(f"找到 {total_files} 个文件")
        self.progress_updated.emit(0, f"找到 {total_files} 个文件", "")

        for i, (file_path, rel_path) in enumerate(files):
            if self.should_stop:
                self.logger.info("处理已停止")
                break

            # 更新进度
            progress = int((i / total_files) * 100)
            self.progress_updated.emit(progress, f"处理文件 {i+1}/{total_files}", rel_path)

            try:
                # 处理文件
                result = self.process_file(file_path)

                # 保存结果
                self.results[rel_path] = result

                # 如果需要保存结果
                if save_results and target_dir:
                    # 创建目标目录
                    target_file_dir = os.path.dirname(os.path.join(target_dir, rel_path))
                    os.makedirs(target_file_dir, exist_ok=True)

                    # 保存结果
                    result_path = os.path.splitext(os.path.join(target_dir, rel_path))[0] + ".json"
                    with open(result_path, 'w', encoding='utf-8') as f:
                        json.dump(result, f, indent=4, ensure_ascii=False)

            except Exception as e:
                self.logger.error(f"处理文件 {rel_path} 失败: {e}")
                self.processing_error.emit(rel_path, str(e))

        # 完成处理
        self.is_processing = False
        self.progress_updated.emit(100, "处理完成", f"共处理 {len(self.results)} 个文件")

        # 生成批量处理报告
        batch_report = self.generate_batch_report()
        self.processing_finished.emit(batch_report)

    def process_file(self, file_path: str) -> Dict[str, Any]:
        """
        处理单个文件

        Args:
            file_path: 文件路径

        Returns:
            Dict[str, Any]: 处理结果
        """
        # 创建音乐数据对象
        music_data = MusicData()
        music_data.file_path = file_path

        # 获取适合的音频格式插件
        plugin = self.audio_format_manager.get_plugin_for_file(file_path)
        if not plugin:
            raise ValueError("不支持的音频格式")

        # 获取音频格式信息
        format_version = plugin.get_version(file_path)

        # 获取元数据
        metadata = plugin.get_metadata(file_path)
        music_data.title = metadata.get("title", os.path.basename(file_path).split('.')[0])
        music_data.artist = metadata.get("artist", "")
        music_data.album = metadata.get("album", "")
        music_data.release_date = metadata.get("year", "")
        music_data.genre = metadata.get("genre", "")

        # 加载音频数据进行分析
        self.logger.info(f"开始加载音频文件: {file_path}")
        audio_data, sample_rate = plugin.load_audio(file_path)

        if audio_data is not None and sample_rate is not None:
            self.logger.info(f"音频加载成功: 采样率={sample_rate}, 数据长度={len(audio_data)}")

            from utils.audio_analyzer import AudioAnalyzer
            analyzer = AudioAnalyzer()
            analyzer.audio_data = audio_data
            analyzer.sample_rate = sample_rate
            analyzer.duration = len(audio_data) / sample_rate

            # 分析音频特征
            try:
                self.logger.info("开始音频特征分析")

                # 分析动态范围
                dynamic_range = analyzer.analyze_dynamic_range()
                music_data.production_data["recording_quality"]["dynamic_range"] = dynamic_range
                self.logger.info(f"动态范围分析完成: {dynamic_range:.2f}dB")

                # 分析底噪电平
                noise_floor = analyzer.analyze_noise_floor()
                music_data.production_data["recording_quality"]["noise_floor"] = noise_floor
                self.logger.info(f"底噪电平分析完成: {noise_floor:.2f}dBFS")

                # 分析前奏吸引力峰值出现时间
                intro_hook_timing = analyzer.analyze_intro_hook_timing()
                music_data.market_data["streaming_adaptation"]["intro_hook_timing"] = intro_hook_timing
                self.logger.info(f"前奏分析完成: {intro_hook_timing:.2f}秒")

                # 分析副歌音高范围
                chorus_range = analyzer.analyze_chorus_range()
                music_data.emotional_data["emotion_delivery"]["chorus_range"] = chorus_range
                self.logger.info(f"副歌音域分析完成: {chorus_range:.2f}度")

                # 添加更多音频分析
                # 分析和弦进行特征
                try:
                    harmony_features = analyzer.analyze_harmony_features()
                    if harmony_features:
                        music_data.technical_data["harmony_progression"]["formula_usage"] = harmony_features.get("formula_usage", 0)
                        music_data.technical_data["harmony_progression"]["key_changes"] = harmony_features.get("key_changes", 0)
                        music_data.technical_data["harmony_progression"]["alt_chord_ratio"] = harmony_features.get("alt_chord_ratio", 0)
                        self.logger.info(f"和声分析完成: 公式化={harmony_features.get('formula_usage', 0):.2f}, 转调={harmony_features.get('key_changes', 0)}, 替代和弦={harmony_features.get('alt_chord_ratio', 0):.2f}")
                except Exception as e:
                    self.logger.warning(f"和声分析失败: {e}")

                # 分析旋律结构特征
                try:
                    melody_features = analyzer.analyze_melody_features()
                    if melody_features:
                        music_data.technical_data["melody_structure"]["interval_range"] = melody_features.get("interval_range", 0)
                        music_data.technical_data["melody_structure"]["hook_density"] = melody_features.get("hook_density", 0)
                        music_data.technical_data["melody_structure"]["reverse_melody"] = melody_features.get("reverse_melody", False)
                        self.logger.info(f"旋律分析完成: 音程跨度={melody_features.get('interval_range', 0):.2f}, Hook密度={melody_features.get('hook_density', 0):.2f}")
                except Exception as e:
                    self.logger.warning(f"旋律分析失败: {e}")

                # 分析流媒体适配特征
                try:
                    streaming_features = analyzer.analyze_streaming_features()
                    if streaming_features:
                        music_data.market_data["streaming_adaptation"]["mobile_optimization"] = streaming_features.get("mobile_optimization", False)
                        self.logger.info(f"流媒体分析完成: 移动端优化={streaming_features.get('mobile_optimization', False)}")
                except Exception as e:
                    self.logger.warning(f"流媒体分析失败: {e}")

                self.logger.info("音频特征分析全部完成")

            except Exception as e:
                self.logger.error(f"音频分析失败: {e}")
                import traceback
                self.logger.error(f"详细错误信息: {traceback.format_exc()}")
        else:
            self.logger.warning(f"音频加载失败: {file_path}")
            # 设置默认值以避免所有参数都是0
            music_data.production_data["recording_quality"]["dynamic_range"] = 8.0
            music_data.production_data["recording_quality"]["noise_floor"] = -65.0
            music_data.market_data["streaming_adaptation"]["intro_hook_timing"] = 4.0
            music_data.emotional_data["emotion_delivery"]["chorus_range"] = 8.0

        # 获取歌词
        self.logger.info(f"开始获取歌词: {file_path}")
        lyrics = plugin.get_lyrics(file_path)

        if lyrics:
            self.logger.info(f"歌词获取成功: 长度={len(lyrics)}字符")
            # 记录歌词的前100个字符用于验证差异化
            preview = lyrics[:100].replace('\n', ' ')
            self.logger.info(f"歌词预览: {preview}...")

            # 分析歌词特征
            try:
                from utils.lyrics_analyzer import LyricsAnalyzer
                lyrics_analyzer = LyricsAnalyzer()

                # 加载歌词
                if lyrics_analyzer.load_lyrics(lyrics):
                    self.logger.info("歌词加载成功，开始分析")

                    # 分析年度热词覆盖率
                    trending_words_ratio = lyrics_analyzer.analyze_trending_words_ratio()
                    music_data.emotional_data["era_resonance"]["trending_words_ratio"] = trending_words_ratio
                    self.logger.info(f"年度热词覆盖率: {trending_words_ratio:.3f}")

                    # 分析通感修辞数量
                    synesthesia_count = lyrics_analyzer.analyze_synesthesia_count()
                    music_data.emotional_data["emotion_delivery"]["synesthesia_count"] = synesthesia_count
                    self.logger.info(f"通感修辞数量: {synesthesia_count}")

                    # 分析具象场景描写密度
                    scene_density = lyrics_analyzer.analyze_scene_density()
                    music_data.emotional_data["narrative_tension"]["scene_density"] = scene_density
                    self.logger.info(f"场景描写密度: {scene_density:.3f}")

                    # 分析歌词语义模糊度
                    lyrics_clarity = lyrics_analyzer.analyze_lyrics_clarity()
                    music_data.emotional_data["narrative_tension"]["lyrics_clarity"] = lyrics_clarity
                    self.logger.info(f"语义模糊度: {lyrics_clarity:.3f}")

                    # 分析情感转折点是否符合三幕剧结构
                    plot_structure = lyrics_analyzer.analyze_plot_structure()
                    music_data.emotional_data["narrative_tension"]["plot_structure"] = plot_structure
                    self.logger.info(f"三幕剧结构: {plot_structure}")

                    # 为歌词分析插件提供数据
                    # 确保每个文件都有独特的歌词分析结果
                    music_data.lyrics = lyrics  # 保存原始歌词

                    # 计算歌词的基本统计信息
                    lines = lyrics.split('\n')
                    words = lyrics.split()
                    unique_words = set(word.lower().strip('.,!?;:"()[]') for word in words if word.strip())

                    # 计算词汇丰富度 (Type-Token Ratio)
                    vocabulary_richness = len(unique_words) / len(words) if len(words) > 0 else 0

                    # 简单的押韵分析
                    rhyme_scheme = self.analyze_simple_rhyme_scheme(lines)

                    # 简单的隐喻密度分析
                    metaphor_density = self.analyze_simple_metaphor_density(lyrics)

                    # 国际传播潜力（基于英文词汇比例）
                    english_words = sum(1 for word in words if word.isascii() and word.isalpha())
                    international_appeal = english_words / len(words) if len(words) > 0 else 0

                    self.logger.info(f"歌词统计: 行数={len(lines)}, 词数={len(words)}, 独特词={len(unique_words)}")
                    self.logger.info(f"词汇丰富度={vocabulary_richness:.3f}, 押韵复杂度={rhyme_scheme:.3f}")
                    self.logger.info(f"隐喻密度={metaphor_density:.3f}, 国际传播潜力={international_appeal:.3f}")

                else:
                    self.logger.warning("歌词加载失败")
            except Exception as e:
                self.logger.error(f"歌词分析失败: {e}")
                import traceback
                self.logger.error(f"详细错误信息: {traceback.format_exc()}")
        else:
            self.logger.warning(f"未找到歌词文件: {file_path}")
            # 设置默认值
            music_data.emotional_data["emotion_delivery"]["synesthesia_count"] = 1

        # 执行评价
        try:
            self.evaluator.evaluate(music_data)

            # 计算总分
            total_score = self.evaluator.calculate_total_score()
            dimension_scores = self.evaluator.calculate_dimension_scores()

            # 获取详细结果
            detailed_results = self.evaluator.get_detailed_results()

            # 添加文件信息
            result = {
                "file_path": file_path,
                "file_name": os.path.basename(file_path),
                "format_version": format_version,
                "metadata": metadata,
                "has_lyrics": lyrics is not None,
                "evaluation": detailed_results
            }

            return result
        except Exception as e:
            self.logger.error(f"评价失败: {e}")
            raise

    def generate_batch_report(self):
        """生成批量处理报告"""
        report = {
            "summary": {
                "total_files": len(self.results),
                "processed_files": len([r for r in self.results.values() if r.get("evaluation")]),
                "failed_files": len([r for r in self.results.values() if not r.get("evaluation")])
            },
            "results": self.results,
            "statistics": self.calculate_statistics()
        }
        return report

    def calculate_statistics(self):
        """计算统计信息"""
        if not self.results:
            return {}

        # 收集所有有效的评价结果
        valid_evaluations = []
        for result in self.results.values():
            evaluation = result.get("evaluation")
            if evaluation and evaluation.get("total_score") is not None:
                valid_evaluations.append(evaluation)

        if not valid_evaluations:
            return {}

        # 计算统计信息
        total_scores = [eval_result["total_score"] for eval_result in valid_evaluations]

        statistics = {
            "average_score": sum(total_scores) / len(total_scores),
            "max_score": max(total_scores),
            "min_score": min(total_scores),
            "score_distribution": {
                "excellent": len([s for s in total_scores if s >= 80]),
                "good": len([s for s in total_scores if 60 <= s < 80]),
                "average": len([s for s in total_scores if 40 <= s < 60]),
                "poor": len([s for s in total_scores if s < 40])
            }
        }

        # 计算各维度平均分
        dimension_totals = {}
        dimension_counts = {}

        for eval_result in valid_evaluations:
            dimension_scores = eval_result.get("dimension_scores", {})
            for dim, score in dimension_scores.items():
                if dim not in dimension_totals:
                    dimension_totals[dim] = 0
                    dimension_counts[dim] = 0
                dimension_totals[dim] += score
                dimension_counts[dim] += 1

        dimension_averages = {}
        for dim in dimension_totals:
            if dimension_counts[dim] > 0:
                dimension_averages[dim] = dimension_totals[dim] / dimension_counts[dim]

        statistics["dimension_averages"] = dimension_averages

        return statistics

    def analyze_simple_rhyme_scheme(self, lines):
        """
        简单的押韵方案分析

        Args:
            lines: 歌词行列表

        Returns:
            float: 押韵复杂度 (0-1)
        """
        if len(lines) < 4:
            return 0.0

        # 提取每行的最后一个词作为韵脚
        rhyme_words = []
        for line in lines:
            words = line.strip().split()
            if words:
                # 取最后一个词的后缀作为韵脚
                last_word = words[-1].lower().strip('.,!?;:"()[]')
                if len(last_word) >= 2:
                    rhyme_words.append(last_word[-2:])  # 取后两个字符

        if len(rhyme_words) < 4:
            return 0.0

        # 计算押韵模式
        rhyme_pairs = 0
        for i in range(len(rhyme_words) - 1):
            for j in range(i + 1, min(i + 4, len(rhyme_words))):  # 检查附近的行
                if rhyme_words[i] == rhyme_words[j]:
                    rhyme_pairs += 1

        # 押韵复杂度：押韵对数 / 可能的最大押韵对数
        max_pairs = min(len(rhyme_words) // 2, 10)
        rhyme_complexity = min(rhyme_pairs / max_pairs, 1.0) if max_pairs > 0 else 0.0

        return rhyme_complexity

    def analyze_simple_metaphor_density(self, lyrics):
        """
        简单的隐喻密度分析

        Args:
            lyrics: 歌词文本

        Returns:
            float: 隐喻密度 (每行隐喻数量)
        """
        # 常见的隐喻标志词
        metaphor_indicators = [
            '像', '如', '似', '仿佛', '好像', '犹如', '宛如', '如同',
            'like', 'as', 'seems', 'appears', 'resembles'
        ]

        lines = lyrics.split('\n')
        metaphor_count = 0

        for line in lines:
            line_lower = line.lower()
            for indicator in metaphor_indicators:
                metaphor_count += line_lower.count(indicator)

        # 计算每行平均隐喻数量
        metaphor_density = metaphor_count / len(lines) if len(lines) > 0 else 0.0

        return metaphor_density

    def stop_processing(self):
        """停止处理"""
        self.should_stop = True
