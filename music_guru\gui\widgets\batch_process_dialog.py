#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Music Guru - 流行音乐评价软件
批量处理对话框

MIT License
Copyright (c) 2025 Music Guru
"""

import os
from PyQt6.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QPushButton, QFileDialog, QLineEdit, QCheckBox,
                            QGroupBox, QDialogButtonBox, QListWidget, QListWidgetItem)
from PyQt6.QtCore import Qt, pyqtSignal


class BatchProcessDialog(QDialog):
    """批量处理对话框"""
    
    # 自定义信号
    process_started = pyqtSignal(str, str, bool)  # 源目录, 目标目录, 是否保存结果
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        self.setWindowTitle("批量处理音乐文件")
        self.setMinimumWidth(600)
        self.setMinimumHeight(400)
        
        # 初始化变量
        self.source_dir = ""
        self.target_dir = ""
        self.save_results = True
        self.file_extensions = [".mp3", ".flac", ".wav", ".ogg", ".m4a", ".aac", ".wma"]
        
        # 创建UI
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        layout = QVBoxLayout(self)
        
        # 源目录选择
        source_group = QGroupBox("源目录")
        source_layout = QHBoxLayout(source_group)
        
        self.source_edit = QLineEdit()
        self.source_edit.setReadOnly(True)
        source_layout.addWidget(self.source_edit)
        
        self.source_button = QPushButton("浏览...")
        self.source_button.clicked.connect(self.browse_source_dir)
        source_layout.addWidget(self.source_button)
        
        layout.addWidget(source_group)
        
        # 目标目录选择
        target_group = QGroupBox("目标目录")
        target_layout = QVBoxLayout(target_group)
        
        self.save_checkbox = QCheckBox("保存处理结果")
        self.save_checkbox.setChecked(True)
        self.save_checkbox.stateChanged.connect(self.toggle_save_results)
        target_layout.addWidget(self.save_checkbox)
        
        target_dir_layout = QHBoxLayout()
        
        self.target_edit = QLineEdit()
        self.target_edit.setReadOnly(True)
        target_dir_layout.addWidget(self.target_edit)
        
        self.target_button = QPushButton("浏览...")
        self.target_button.clicked.connect(self.browse_target_dir)
        target_dir_layout.addWidget(self.target_button)
        
        target_layout.addLayout(target_dir_layout)
        
        layout.addWidget(target_group)
        
        # 文件类型选择
        extensions_group = QGroupBox("文件类型")
        extensions_layout = QVBoxLayout(extensions_group)
        
        self.extension_checkboxes = {}
        for ext in self.file_extensions:
            checkbox = QCheckBox(ext)
            checkbox.setChecked(True)
            extensions_layout.addWidget(checkbox)
            self.extension_checkboxes[ext] = checkbox
        
        layout.addWidget(extensions_group)
        
        # 文件预览
        preview_group = QGroupBox("文件预览")
        preview_layout = QVBoxLayout(preview_group)
        
        self.file_list = QListWidget()
        preview_layout.addWidget(self.file_list)
        
        self.file_count_label = QLabel("找到 0 个文件")
        preview_layout.addWidget(self.file_count_label)
        
        layout.addWidget(preview_group)
        
        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.StandardButton.Ok | QDialogButtonBox.StandardButton.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)
    
    def browse_source_dir(self):
        """浏览源目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择源目录")
        if dir_path:
            self.source_dir = dir_path
            self.source_edit.setText(dir_path)
            self.update_file_preview()
    
    def browse_target_dir(self):
        """浏览目标目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择目标目录")
        if dir_path:
            self.target_dir = dir_path
            self.target_edit.setText(dir_path)
    
    def toggle_save_results(self, state):
        """切换是否保存结果"""
        self.save_results = state == Qt.CheckState.Checked.value
        self.target_edit.setEnabled(self.save_results)
        self.target_button.setEnabled(self.save_results)
    
    def update_file_preview(self):
        """更新文件预览"""
        self.file_list.clear()
        
        if not self.source_dir or not os.path.exists(self.source_dir):
            self.file_count_label.setText("找到 0 个文件")
            return
        
        # 获取选中的文件扩展名
        selected_extensions = [ext for ext, checkbox in self.extension_checkboxes.items() 
                              if checkbox.isChecked()]
        
        # 遍历目录，查找符合条件的文件
        file_count = 0
        for root, dirs, files in os.walk(self.source_dir):
            for file in files:
                file_ext = os.path.splitext(file)[1].lower()
                if file_ext in selected_extensions:
                    rel_path = os.path.relpath(os.path.join(root, file), self.source_dir)
                    self.file_list.addItem(rel_path)
                    file_count += 1
        
        self.file_count_label.setText(f"找到 {file_count} 个文件")
    
    def accept(self):
        """确认按钮点击事件"""
        if not self.source_dir:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", "请选择源目录")
            return
        
        if self.save_results and not self.target_dir:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", "请选择目标目录")
            return
        
        # 获取选中的文件扩展名
        selected_extensions = [ext for ext, checkbox in self.extension_checkboxes.items() 
                              if checkbox.isChecked()]
        
        if not selected_extensions:
            from PyQt6.QtWidgets import QMessageBox
            QMessageBox.warning(self, "警告", "请至少选择一种文件类型")
            return
        
        # 发送信号
        self.process_started.emit(self.source_dir, self.target_dir, self.save_results)
        
        super().accept()
